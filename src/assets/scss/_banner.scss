.banner-table {
  .simple-table-column {
    padding-top: 5px;
    padding-bottom: 5px;
  }

  .simple-table-head-column {
    width: auto;
  }

  .simple-table-column-uuid,
  .simple-table-column-expired-uuid {
    .banner-uuid {
      width: 100px;
      word-break: break-all;
    }
  }

  .simple-table-column-actions,
  .simple-table-column-expired-actions {
    width: 60px;
  }
}

.banner-item-preview-type {
  width: fit-content;
  height: auto;
  margin-bottom: 5px;
  padding: 1px 13px;
  border: 1px solid $green-light;
  border-radius: 4px;
}

.select-banner-modal {
  font-family: $font-family-averta;

  .select-banner-modal-content {
    padding: 12px 30px;
    width: 471px;

    .select-banner-modal-checkbox {
      text-align: initial;
      padding: 12px;

      .banner-text {
        width: 110px;
        height: 44px;
        margin-bottom: 10px;
      }

      .control-radio-group {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }
    }

    .select-banner-modal-btn-container {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
    }
  }
}

.banner-schedule-form-modal-container {
  font-family: $font-family-averta;
  width: 519px;
  height: 215px;
  overflow: hidden;

  .banner-schedule-sub-container {
    .banner-schedule-top-container {
      display: flex;
      justify-content: space-between;
      padding: 10px 30px;

      .close-icon {
        width: 24px;
        height: 24px;
        cursor: pointer;

        .close-icon-image {
          height: 100%;
          width: 100%;
        }
      }
    }

    .banner-schedule-date-picker-container {
      display: flex;
      margin-top: 15px;
      margin-left: 30px;
    }

    .date-picker-container {
      position: relative;
    }

    .banner-schedule-bottom-container {
      position: relative;
      top: 50px;
      right: 20px;

      .banner-schedule-button-section {
        display: flex;
        gap: 10px;
        float: right;

        .disable-schedule-button {
          pointer-events: none;
          opacity: 0.5;
        }
      }
    }
  }
}
