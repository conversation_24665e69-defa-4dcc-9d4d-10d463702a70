.popup-position {
  position: inherit;
}
.main-inner-section {
  .about-first-edit-ingredient-products {
    cursor: default;
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;

    .back-to-ingredient-btn {
      margin: 20px 0px;
      position: relative;
      left: -30px;
      width: max-content;

      .back-arrow-image-edit-ingredient-products {
        position: relative;
        top: -2px;
        left: 30px;
        width: 18px;
        height: 14px;
        cursor: pointer;
      }

      .back-to-ingredient {
        margin: 0px 4px;
        color: $green;
        cursor: pointer;
        padding: 2px 2px 2px 30px;
      }
    }

    .form-title {
      position: absolute;
      top: 80px;
      padding-top: 14px;

      .form-title-header {
        color: $slate-gray;
        text-transform: uppercase;
        font-size: $font-size-base;
      }

      .switch {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 26px;
        left: 10px;
      }

      .published {
        opacity: 0.5;
        pointer-events: none;
      }

      .switch input {
        opacity: 0;
        width: 0;
        height: 0;
      }

      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: $light-white;
        -webkit-transition: 0.4s;
        transition: 0.4s;
      }

      .slider:before {
        position: absolute;
        content: "";
        height: 20px;
        width: 20px;
        left: 2px;
        bottom: 3px;
        background-color: $white;
        -webkit-transition: 0.4s;
        transition: 0.4s;
      }

      input:checked + .slider {
        background-color:$lightning-yellow;
      }

      input:focus + .slider {
        box-shadow: 0 0 1px $grainsboro;
      }

      input:checked + .slider:before {
        -webkit-transform: translateX(26px);
        -ms-transform: visibleateX(26px);
        transform: translateX(26px);
      }

      .slider.round {
        border-radius: 34px;
      }

      .slider.round:before {
        border-radius: 50%;
      }
    }

    .main-btn-text-edit-ingredient-products {
      display: flex;
      justify-content: space-between;
      margin: 14px 0px;

      .head-btn-edit-ingredient-products {
        display: flex;
        justify-content: space-between;
        gap: 20px;
      }
    }
  }

  .hr-line {
    width: 100%;
    margin-top: 17px;
    color: $grainsboro;
    height: 1px;
  }

  .text-overwrites {
    margin-top: 20px;
    color: $gunmetal-grey;
    font-size: 16px;
    width: 574px;
    margin-left: 10px;

    .info-icon {
      position: relative;
      bottom: 1px;
    }
  }

  .ingredient-name-container {
    justify-content: space-between;
    width: 100%;
    background-color: $white;
    border: 2px solid $grainsboro-mist;
    border-radius: 8px;
    padding-top: 25px;
    padding-bottom: 25px;
    margin-top: 16px;
    height: auto;
    padding-left: 13px;
    padding-right: 23px;

    .ingredient-top-main {
      display: flex;
      align-items: baseline;
      width: 98%;

      .ingredient-header {
        min-width: fit-content;
        color: $gunmetal-grey;
      }

      .ingredient-name {
        color: $copper-rust;
        margin-left: 6px;
      }

      .edit-btn {
        margin-right: 36px;
        margin-left: 10px;
        border-radius: 4px;
        box-shadow: 0px 1px 5px 0px $box-shadow;
        height: 32px;
        width: 32px;
        min-width: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: $white;
        cursor: pointer;

        img {
          width: 16px;
          height: 16px;
        }
      }
    }

    .shoppable-drop-down {
      cursor: pointer;
      display: flex;
      position: absolute;
      right: 40px;
      margin-top: 63px;
      border-radius: 6px;
      border: 1px solid $grainsboro;
      background: $white;
      width: 300px;
      height: 44px;
      padding: 10px;
      margin-right: 23px;

      .shoppable-drop-down-container {
        display: flex;

        .shoppable-container {
          display: flex;

          .shoppable-drop-down-image {
            width: 23px;
            height: 22px;
          }

          .shoppable-drop-down-data {
            color: $jet-black;
            margin-top: 3px;
            margin-left: 12px;
          }
        }
      }

      .shoppable-dropdown-icon {
        position: absolute;
        transform: rotate(90deg);
        width: 8px;
        height: 12px;
        cursor: pointer;
        right: 20px;
        top: 15px;
      }
    }

    .shoppable-drop-down2 {
      margin-top: 39px;
    }

    .autocomplete-results {
      margin-top: 35px;
      right: 11px;
      width: 263px;
      position: absolute;
      list-style: none;
      box-shadow: 0 1px 10px 0 $box-shadow;
      background: $white;
      z-index: 9;
      color: $charcoal-light;
      overflow-y: scroll;
      border-radius: 4px;
      scrollbar-width: none;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    .autocomplete-result {
      padding: 8px 20px;
      color: $jet-black;
      cursor: pointer;
      margin: 2px;
      border-radius: 4px;

      &.is-active,
      &:hover {
        background: $green;
        color: $white;
      }
    }

    .view-all-main-container {
      display: flex;
      position: relative;
      left: 172px;
      top: 5px;

      .pl-containeer-name {
        color: $gunmetal-grey;
        margin-right: 9px;
      }

      .view-all-text {
        position: relative;
        width: fit-content;
        height: auto;
        color: $green-light;
        cursor: pointer;
      }

      .view-all-text:hover {
        text-decoration: underline;
      }
    }

    .hr-line {
      width: 100%;
      margin-top: 17px;
      color: $grainsboro;
      height: 1px;
    }

    .text-overwrites {
      margin-top: 20px;
      color: $gunmetal-grey;
      font-size: 16px;
      width: 574px;
      margin-left: 10px;

      .info-icon {
        position: relative;
        bottom: 1px;
      }
    }
  }

  .recipes-main-container {
    width: 100%;
    background-color: $white;
    border: 2px solid $grainsboro-mist;
    border-radius: 8px;
    padding-top: 25px;
    padding-bottom: 25px;
    margin-top: 16px;

    .recipes-sub-container {
      margin: 0 20px;

      .recipes-top-section {
        display: flex;

        .recipes-text-section {
          .recipe-count {
            color: $black;
          }
        }
      }

      .recipes-middle-section {
        margin-top: 12px;
        position: relative;

        .recipes-container {
          display: flex;
          justify-content: space-between;

          .recipes-section-While-loading {
            .loading-content {
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              width: 559px;
              min-height: 185px;
              padding: 0 20px;

              .input-loading {
                height: 60px;
                display: flex;
                justify-content: center;

                .loader-image {
                  border: 3px solid $white;
                  border-radius: 50%;
                  border-top: 3px solid $green;
                  border-right: 3px solid $green;
                  border-bottom: 3px solid $green;
                  width: 20px;
                  height: 20px;
                  -webkit-animation: spin 2s linear infinite;
                  animation: spin 2s linear infinite;
                }
              }

              .loading-text {
                background-color: $green-peppermint;
                border-radius: 4px;
                border: 1px solid $green-fringy-flower;
                width: 468px;
                height: 57px;
                text-align: center;

                p {
                  font-weight: 400;
                  font-size: 16px;
                  color: $shadow-gray;
                  padding: 18px 0px;
                }
              }
            }

            .loading-state-recipe {
              display: flex;
              align-items: center;
              height: 100%;
              position: absolute;
              justify-content: center;
              width: 100%;

              .loader-image {
                position: absolute;
                border: 3px solid $white;
                border-radius: 50%;
                border-top: 3px solid $green;
                border-right: 3px solid $green;
                border-bottom: 3px solid $green;
                width: 22px;
                top: 50%;
                height: 22px;
                -webkit-animation: spin 2s linear infinite;
                animation: spin 2s linear infinite;
              }
            }
          }

          .recipes-section {
            border-radius: 8px;
            border: 1px solid $whisper;
            height: 186px;
            width: 144px;
            margin-left: 16px;
            cursor: pointer;
            position: relative;

            &:hover {
              background-color: $light-mint;
              border: 1px solid $green;
            }

            .loading-state-recipe {
              display: flex;
              align-items: center;
              height: 100%;
              position: absolute;
              justify-content: center;
              width: 100%;

              .loader-image {
                position: absolute;
                border: 3px solid $white;
                border-radius: 50%;
                border-top: 3px solid $green;
                border-right: 3px solid $green;
                border-bottom: 3px solid $green;
                width: 22px;
                top: 51%;
                height: 22px;
                -webkit-animation: spin 2s linear infinite;
                animation: spin 2s linear infinite;
              }

              .loader-text {
                background: $green-peppermint;
                text-align: center;
                position: absolute;
                top: 66%;
                font-size: 12px;
                width: 87%;
                color: $black;
                border-radius: 8px;
                border: 1px solid $green;
                padding: 11px 0px;
              }
            }

            .inner-recipe-card {
              padding-top: 5px;

              .recipe-card-exclamatory-image {
                display: flex;
                justify-content: right;
                height: 0px;

                &.loading-recipe-phase {
                  opacity: 0.5;
                }

                img {
                  height: 16px;
                  width: 16px;
                  margin-right: 8px;
                }
              }

              .recipe-image {
                display: flex;
                justify-content: center;
                margin-top: 15px;

                &.loading-recipe-phase {
                  opacity: 0.5;
                }

                img {
                  object-fit: cover;
                  height: 70px;
                  width: 70px;
                }
              }

              .recipe-title {
                padding: 0 5px;
                margin-top: 10px;
                text-align: center;
                color: $black;
                .recipe-title-text {
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }

              .recipe-id {
                margin-top: 5px;
                text-align: center;
                color: $stone-gray;
                font-size: 10px;
              }
            }

            &.loading-phase {
              pointer-events: none;
              background: $white;
            }
          }

          .recipes-empty-section {
            height: 186px;
            width: 144px;
            margin-left: 16px;
            position: relative;
          }
        }

        .next-button-recipe {
          position: relative;
          right: 12px;
        }

        .prev-button-recipe {
          position: relative;
          right: 3px;
        }

        .recipe-page-prev,
        .recipe-page-next {
          margin: auto 0 auto 16px;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: $white;
          box-shadow: 0 0 10px $box-shadow;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          &.disabled {
            cursor: default;
            pointer-events: none;
            opacity: 0.5;
          }
        }

        .recipe-page-prev:before,
        .recipe-page-next:before {
          content: "";
          display: block;
          background: url(../images/green-right-arrow.png) no-repeat center
            center;
          width: 16px;
          height: 17px;
          margin: 0 auto;
        }

        .recipe-page-prev:before {
          transform: rotate(180deg);
        }
      }

      .recipes-bottom-container {
        margin-top: 13px;

        span {
          font-size: 12px;
          color: $gunmetal-grey;
          font-weight: 700;
        }
      }
    }
  }

  .about-second-edit-ingredient-products {
    font-weight: 300;
    cursor: default;

    .container-edit-ingredient-products {
      float: left;
      width: 100%;
      margin: 30px auto;
      color: $white;
    }

    ul {
      list-style-type: none;
      margin-left: 20px;
    }

    .content-edit-ingredient-products {
      color: $black;
      background-color: $white;
      border-radius: 8px;
    }

    .loading {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      min-height: 255px;
      padding: 0 20px;
      border: 2px solid $grainsboro-mist;
      border-radius: 8px;

      .input-loading {
        height: 60px;
        display: flex;
        justify-content: center;

        .loader-image {
          border: 3px solid $white;
          border-radius: 50%;
          border-top: 3px solid $green;
          border-right: 3px solid $green;
          border-bottom: 3px solid $green;
          width: 20px;
          height: 20px;
          -webkit-animation: spin 2s linear infinite;
          animation: spin 2s linear infinite;
        }
      }

      .loading-text {
        background-color: $green-peppermint;
        border-radius: 4px;
        border: 1px solid $green-fringy-flower;
        width: 468px;
        height: 57px;

        p {
          font-weight: 400;
          font-size: 16px;
          color: $shadow-gray;
          padding: 18px 0px;
          text-align: center;
        }
      }
    }

    .tabcontent-edit-ingredient-products {
      padding: 20px;
      padding-left: 0px;
      padding-right: 0px;
      border-radius: 8px;
      box-shadow: 3px 3px 6px 3px $gentle-gainsboro-gray;

      .selection-container {
        .selection-panel {
          display: flex;
          align-items: center;
          margin-bottom: 15px;
          position: relative;
          padding-left: 20px;
          padding-right: 20px;

          .select-all-text {
            position: absolute;
            left: 49px;
            margin-left: 10px;
            font-weight: 700;
            font-size: 14px;
            color: $black-bean;
          }

          .selection {
            display: flex;
            width: 58%;

            .selected-text {
              font-size: 16px;
              font-weight: 400;
              color: $black;
              position: absolute;
              left: 136px;
              bottom: 4px;

              .total-recipe-selected-cross-icon {
                width: 10px;
                height: 10px;
                margin-left: 13px;
              }

              .total-recipe-selected-cross-icon:hover {
                cursor: pointer;
              }
            }
          }

          .btn-container {
            display: flex;
            position: absolute;
            right: 247px;
            align-items: center;
          }

          .cancel-btn {
            color: $kelly-green;
            width: 57px;
            height: 18px;
            position: absolute;
            right: 20px;
            border: none;
            background: none;
          }

          .cancel-btn:hover {
            cursor: pointer;
          }

          .select-all-checbox-section {
            width: 4%;
            margin-left: 9px;
            padding-top: 4px;

            .recipes-selection-checkbox-section {
              display: block;
              position: relative;
              font-size: 22px;
              -webkit-user-select: none;
              -moz-user-select: none;
              -ms-user-select: none;
              user-select: none;
              width: 24px;
              height: 24px;

              input {
                opacity: 0;
                height: 0;
                width: 0;

                &:checked {
                  ~ {
                    .checkmark {
                      background-color: $green-light;
                      border: 3px solid $green-light;

                      &:after {
                        display: block;
                      }
                    }
                  }
                }
              }

              .checkmark {
                position: absolute;
                top: -3px;
                left: 0;
                height: 24px;
                width: 24px;
                color: $grainsboro;
                background-color: $white;
                border: 3px solid $grainsboro;
                border-radius: 4px;
                cursor: pointer;

                &:hover {
                  border: 3px solid $green-light;
                }

                &:after {
                  content: "";
                  position: absolute;
                  display: none;
                  left: 6px;
                  top: 2px;
                  width: 6px;
                  height: 12px;
                  border: solid $white;
                  border-width: 0 2px 2px 0;
                  -webkit-transform: rotate(45deg);
                  -ms-transform: rotate(45deg);
                  transform: rotate(45deg);
                  font-weight: 800;
                }
              }

              .select-all-recipes {
                display: flex;
                font-size: 14px;
                font-weight: 700;
                color: $black $jet-black;
                cursor: pointer;
              }
            }
          }
        }
      }

      .not-shoppable-main-container {
        margin-bottom: 15px;

        .not-shoppable-content {
          padding-left: 20px;
          span {
            font-size: 14px;
            font-weight: 400;
            color: $red-orange;
          }
        }
      }

      .promoted-ingredient-main-container {
        padding-left: 20px;
        padding-right: 20px;

        .promoted-matches-ingredient-head {
          width: 100%;
          color: $black;
          margin-bottom: 0;
        }

        .right-ingredient-section {
          position: relative;

          .published {
            opacity: 0.5;
            pointer-events: none;
          }
          .publish-btn {
            bottom: 5px;
            right: 0px;
            position: absolute;
            display: flex;
            .text {
              position: relative;
              left: 14px;
              top: 4px;
              font-weight: 700;
              font-size: 16px;
              color: $green-light;
            }

            .disable {
              opacity: 0.5;
            }

            #disable-div {
              position: relative;

              #disable {
                opacity: 0.5;
                pointer-events: none;
              }

              .switch {
                position: relative;
                display: inline-block;
                width: 42px;
                height: 26px;
                margin-left: 20px;
                input {
                  opacity: 0;
                  width: 0;
                  height: 0;
                }
              }
              .slider-round {
                position: absolute;
                cursor: pointer;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: $light-white;
                -webkit-transition: 0.4s;
                transition: 0.4s;
                border-radius: 30px;
                &:before {
                  position: absolute;
                  content: "";
                  height: 23px;
                  width: 23px;
                  left: 2px;
                  bottom: 2px;
                  background-color: $white;
                  -webkit-transition: 0.4s;
                  transition: 0.4s;
                  border-radius: 50%;
                }
              }
              input {
                &:checked {
                  + {
                    .slider-round {
                      background-color: $green;
                      &:before {
                        -webkit-transform: translateX(15px);
                        -ms-transform: translateX(15px);
                        transform: translateX(15px);
                      }
                    }
                  }
                }
                &:focus {
                  + {
                    .slider-round {
                      box-shadow: 0 0 1px $green;
                    }
                  }
                }
              }
            }
          }
        }

        .promoted-matches-ingredient-text {
          width: 100%;
          color: $grey;
          font-size: 16px;
          margin-top: 8px;
          font-weight: 400;
        }
      }
      .promoted-matches-ingredient-details-table {
        margin-top: 25px;

        .main-table-edit-ingredient-product {
          .product-table-ingredient-head {
            background-color: $pearl-mist;
            border-radius: 8px 8px 0px 0px;
            padding-left: 20px;
            padding-right: 20px;
            .product-ingredient-title {
              display: flex;
              color: $spanish-gray;
              font-size: 14px;
              height: 28px;
              font-family: $font-family-averta-bold;
              line-height: 1;

              .product-table-srno {
                display: grid;
                align-content: center;
                min-width: 22px;
                margin-left: 15px;
              }

              .product-table-image-container {
                display: grid;
                align-content: center;
                min-width: 60px;
              }

              .product-name {
                display: grid;
                align-content: center;
                margin-left: 20px;
                width: 100%;
                font-weight: $font-weight-bold;
              }

              .product-id {
                min-width: 105px;
                width: 105px;
                display: grid;
                align-content: center;
                margin-left: 28px;
                font-weight: $font-weight-bold;
              }

              .product-button-container {
                width: 120px;
                min-width: 120px;
                margin-left: 28px;
                display: grid;
                align-content: center;
              }

              .product-menu {
                display: grid;
                min-width: 58px;
              }
            }
          }
        }

        .table-body-edit-ingredient-product {
          #add-padding {
            padding-left: 20px;
            padding-right: 20px;
          }

          .edit-ingredient-promoted-table {
            position: relative;
            display: flex;
            border-top: 1px solid $bright-gray;
            border-bottom: 1px solid $bright-gray;
            border-left: 1px solid $white;
            border-right: 1px solid $white;
            padding-left: 20px;
            padding-right: 20px;
            .promoted-handle {
              position: absolute;
              left: 50%;
              top: 0%;
              transform: translate(-50%, 0%);
              width: fit-content;
              height: fit-content;
              display: none;
              cursor: move;
            }

            &:hover {
              background-color: $aqua-spring;
              border: 1px solid $green-light;

              .promoted-handle {
                display: block;
              }
            }

            #selection-enabled {
              margin-right: 3px;
            }

            .promoted-table-srno {
              font-weight: 400;
              color: $black;
              font-size: 14px;
              display: grid;
              align-content: center;
              min-width: 22px;
              margin-left: 15px;
            }

            .promoted-table-image-container {
              display: grid;
              align-content: center;

              .promoted-table-image {
                width: 60px;
                min-width: 60px;
                height: 60px;
                min-height: 60px;
                margin: 10px 0px;
                display: grid;
                align-content: center;
                justify-content: center;

                .promoted-image {
                  width: auto;
                  height: auto;
                  max-height: 60px;
                  max-width: 60px;
                }
              }
            }

            .promoted-name {
              color: $black;
              display: grid;
              align-content: center;
              margin-left: 20px;
              width: 100%;
              padding: 26px 0px;
            }

            .promoted-id {
              min-width: 105px;
              width: 105px;
              color: $grey;
              display: grid;
              align-content: center;
              margin-left: 28px;
              padding: 10px 0px;
              word-break: break-all;
            }

            .promoted-button {
              width: 120px;
              min-width: 120px;
              margin-left: 28px;
            }

            .promoted-menu {
              padding: 12px 15px;
              display: grid;
              align-content: center;

              .menu-container {
                background-color: $white;
                border-radius: 10px;
                width: 28px;
                height: 20px;
                cursor: pointer;
                display: flex;
                align-items: center;
                position: relative;

                .table-edit-btn {
                  width: 17px;
                  height: 5px;
                  padding: 0;
                  margin: 0 auto;
                  object-fit: cover;
                  z-index: 1;
                }

                &:hover {
                  background-color: $pure-white;
                }

                .menu-box {
                  display: block;
                  position: absolute;
                  right: 1%;
                  width: 151px;
                  top: 56%;
                  transform: translate(-1%, 11%);
                  z-index: 2;
                  box-shadow: 0 4px 10px 0 $shadow-black,
                    0 3px 5px 0 $faint-black,
                    0 0 0 1px $shadowy-black;
                  border-radius: 4px;
                  background: $white;

                  .menu-list {
                    list-style: none;
                    background: $white;
                    border-radius: 8px;
                    margin: 11px 5px;

                    li {
                      display: flex;
                      align-items: center;
                      padding: 6px 0px;
                      width: 141px;
                      font-size: 16px;
                      color: $black;
                      font-weight: 700;
                      padding-left: 10px;

                      &:hover {
                        color: $white;
                        background: $green;
                        cursor: pointer;
                      }
                    }

                    .hide-data {
                      display: none;
                    }
                  }
                }
              }

              .menu-selected {
                background-color: $aqua-spring;

                &:hover {
                  background-color: $aqua-spring;
                }
              }
            }
          }
        }
      }

      .zero-promoted-matches-ingredient-table {
        width: 100%;
        color: $spanish-gray;
        font-size: 15px;
        background-color: $pearl-mist;
        border: 2px solid $grainsboro-mist;
        border-radius: 8px;
        padding-top: 25px;
        padding-bottom: 25px;
        margin-top: 16px;
        text-align: center;
        font-weight: 700;

        span {
          font-weight: 400;
        }
      }

      .filter-search-section {
        display: flex;
        justify-content: space-between;
        margin-top: 45px;
        padding-left: 20px;
        padding-right: 20px;
      }

      .table-head-edit-ingredient-products {
        margin-bottom: 10px;
        display: flex;
        flex-direction: column;

        .total-product-counting {
          color: $black;
        }

        .disable-counting {
          opacity: 0.5;
          pointer-events: none;
        }

        .filter-section {
          display: flex;
          font-weight: $font-weight-bold;
          color: $slate-gray;

          .filter-zone-section {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 11px 20px 11px 4px;

            .filter-icon {
              height: 24px;
              width: 16px;
            }

            .filter-text {
              font-size: 14px;
            }
          }

          .search-brand-popup {
            height: 44px;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .brand-details {
            display: flex;
            border: 1px solid $grainsboro;
            border-radius: 22px;
            width: auto;
            height: 36px;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            padding: 10px 30px;
            cursor: pointer;

            .brand-title {
              font-size: 16px;
              margin-left: 0px;
            }

            .brand-selected {
              margin: 0 8px;
              color: $green-light;
              font-size: 16px;
            }

            .brand-dropdown-icon {
              transform: rotate(90deg);
              width: 20px;
              height: 20px;
              cursor: pointer;
              margin-right: 7px;
            }
          }

          .line {
            height: 30px;
            width: 1px;
            background-color: $bright-gray;
            position: relative;
            left: -46px;
            margin: 0 11px;
          }

          .exit-box-section {
            position: relative;
            right: 58px;
            width: 33px;
            height: 100%;
          }

          .exit-brand-icon {
            height: 15px;
            width: 15px;
            position: relative;
            top: 10px;
            left: 7px;
            cursor: pointer;
          }
        }
      }

      .search-box-add-product-button {
        display: flex;
        flex-direction: column;
        .search-box-edit-product {
          float: right;
          background-color: $pristine-white;
          border: 1px solid $grainsboro-gray;
          border-radius: 30px;
          padding: 0 12px 0 16px;
          height: 46px;
          width: 345px;

          .search-input-box {
            width: 302px;
            height: 40px;
            margin: 2px 0px 2px 24px;
            padding: 2px 0px 2px 8px;
            background: none;
            color: $spanish-gray;
            caret-color: $green;
            border: none;
            border-radius: 0;
            box-shadow: none;
          }

          .align-search-input-box {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 272px;
            display: block;
          }

          .exit-search-icon {
            width: 18px;
            height: 18px;
            position: relative;
            bottom: 31px;
            float: right;
            cursor: pointer;
          }

          .search-icon-green-edit-product-image {
            position: relative;
            top: -33px;
            float: left;
            height: 24px;
            cursor: pointer;
            text-align: right;
            vertical-align: middle;
          }
        }

        .select-button {
          width: 99%;
          justify-content: end;
          display: flex;
          margin-top: 16px;
          height: 18px;
          color: $kelly-green;

          span:hover {
            cursor: pointer;
          }
        }

        .add-product-button {
          cursor: pointer;
          height: 24px;
          margin-right: 20px;
          position: relative;
          top: 6px;
          margin-left: 27px;
          margin-top: 5px;

          .add-product-image {
            width: 18px;
            height: 18px;
          }
        }
      }

      .ingredient-search-brand-main {
        position: absolute;
        z-index: 9;
        width: auto;
        height: auto;
        border-radius: 4px;
        overflow-y: visible !important;
        background-color: $white;
        box-shadow: 0 10px 14px 0 $box-shadow;
        scrollbar-color: $grainsboro $whispering-white-smoke;
        scrollbar-width: thin;

        ::-webkit-scrollbar {
          width: 12px;
          border-radius: 3px;
        }

        ::-webkit-scrollbar-track {
          background: $whispering-white-smoke;
        }

        ::-webkit-scrollbar-thumb {
          background: $grainsboro;
          border: 3px solid $transparent;
          border-radius: 15px;
          background-clip: content-box;
        }

        .ingredient-brand-search-bar {
          background-color: $white;
          position: static;
          height: 84px;

          .search-bar-content {
            position: relative;
            top: 30px;
            left: 30px;
            background-color: $pristine-white;
            border: 1px solid $grainsboro-gray;
            border-radius: 30px;
            padding: 0 0px 0 16px;
            height: 36px;
            width: 342px;
            margin-bottom: -40px;

            .search-icon-grey-image {
              position: relative;
              top: 7px;
              height: 18px;
              width: 20px;
              cursor: pointer;
            }

            .search-bar-text {
              position: relative;
              left: 3px;
              width: 296px;
              top: 7px;
              background: none;
              color: $graphite-gray;
              caret-color: $green;
              border: none;
              border-radius: 0;
              box-shadow: none;
            }

            .align-search-input-box {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              width: 272px;
            }

            .exit-search-icon {
              width: 14px;
              height: 14px;
              position: absolute;
              top: 11px;
              right: 8px;
              float: right;
              cursor: pointer;
              display: none;
            }
          }
        }

        .ingredient-search-brand-data-main {
          overflow-y: scroll;
          max-height: 314px;

          .table-image-loader {
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9;
            background-color: $white;
            margin: 0 auto;
            height: 200px;
            width: 210px;

            .loader {
              border: 3px solid $pristine-white;
              border-radius: 50%;
              border-top: 3px solid $spanish-gray;
              border-right: 3px solid $spanish-gray;
              border-bottom: 3px solid $spanish-gray;
              width: 24px;
              height: 24px;
              -webkit-animation: spin 2s linear infinite;
              animation: spin 2s linear infinite;
            }
            @-webkit-keyframes spin {
              0% {
                -webkit-transform: rotate(0deg);
              }

              100% {
                -webkit-transform: rotate(360deg);
              }
            }

            @keyframes spin {
              0% {
                transform: rotate(0deg);
              }

              100% {
                transform: rotate(360deg);
              }
            }
          }

          .brand-details-checkbox {
            width: 100%;
            cursor: pointer;

            .ingredient-brand-data {
              display: flex;
              width: 100%;
              padding: 12px;

              &:hover {
                background: $aqua-spring !important;
              }

              .brand-search-list-data {
                display: flex;
                justify-content: space-between;
                width: 77%;
                position: relative;
                top: 2px;
                left: 54px;
              }
            }

            .load-button {
              width: 140px;
              margin: 0 auto;
              margin-top: 10px;
            }
          }

          .add-ingredients-background {
            background-color: RGB(231, 246, 228);
          }
        }

        .no-result-for-brand {
          display: flex;
          justify-content: space-around;
          margin-top: 14px;
          color: $shadow-gray;
        }

        .apply-btn-container {
          position: relative;
          top: 14px;

          .filter-save-btn {
            display: flex;
            justify-content: center;
            position: sticky;
            background-color: $white;
            width: 414px;
            height: 83px;
            padding: 21px;
          }
        }
      }

      .ingredient-search-main-container {
        overflow-y: hidden !important;
        scrollbar-color: $grainsboro $whispering-white-smoke;
      }

      .crud-table-edit-ingredient-product {
        .main-table-edit-ingredient-product {
          .product-table-ingredient-head {
            background-color: $pearl-mist;
            border-radius: 8px 8px 0px 0px;
            .product-ingredient-title {
              display: flex;
              color: $spanish-gray;
              font-size: 14px;
              height: 28px;
              font-family: $font-family-averta-bold;
              line-height: 1;

              .product-table-srno {
                display: grid;
                align-content: center;
                min-width: 22px;
                margin-left: 15px;
              }

              .product-table-image-container {
                display: grid;
                align-content: center;
                min-width: 60px;
              }

              .product-name {
                display: grid;
                align-content: center;
                margin-left: 20px;
                width: 100%;
                font-weight: $font-weight-bold;
              }

              .product-id {
                min-width: 105px;
                width: 105px;
                display: grid;
                align-content: center;
                margin-left: 28px;
                font-weight: $font-weight-bold;
              }

              .product-button-container {
                width: 120px;
                min-width: 120px;
                margin-left: 28px;
                display: grid;
                align-content: center;
              }

              .product-menu {
                display: grid;
                min-width: 58px;
              }
            }
          }

          #one {
            padding-left: 20px;
            padding-right: 20px;
          }

          .table-body-edit-ingredient-product {
            #delete-selected {
              background: $aqua-spring !important;
            }

            .hover-delete:hover {
              background: $aqua-spring !important;
              cursor: pointer;
            }

            .edit-ingredient-product-table {
              position: relative;
              display: flex;
              border-top: 1px solid $bright-gray;
              border-bottom: 1px solid $bright-gray;
              border-left: 1px solid $white;
              border-right: 1px solid $white;
              .product-table-srno-checkbox {
                font-weight: 400;
                color: $black;
                font-size: 14px;
                display: grid;
                align-content: center;
                min-width: 22px;
                padding-top: 15px;
                margin-right: 10px;
                margin-left: 9px;

                .select-all-checbox-section {
                  width: 2%;

                  .recipes-selection-checkbox-section {
                    display: block;
                    position: relative;
                    font-size: 22px;
                    -webkit-user-select: none;
                    -moz-user-select: none;
                    -ms-user-select: none;
                    user-select: none;

                    input {
                      opacity: 0;
                      height: 0;
                      width: 0;

                      &:checked {
                        ~ {
                          .checkmark {
                            background-color: $green-light;
                            border: 3px solid $green-light;

                            &:after {
                              display: block;
                            }
                          }
                        }
                      }
                    }

                    .checkmark {
                      position: absolute;
                      top: -3px;
                      left: 0;
                      height: 24px;
                      width: 24px;
                      color: $grainsboro;
                      background-color: $white;
                      border: 3px solid $grainsboro;
                      border-radius: 4px;
                      cursor: pointer;

                      &:hover {
                        border: 3px solid $green-light;
                      }

                      &:after {
                        content: "";
                        position: absolute;
                        display: none;
                        left: 6px;
                        top: 2px;
                        width: 6px;
                        height: 12px;
                        border: solid $white;
                        border-width: 0 2px 2px 0;
                        -webkit-transform: rotate(45deg);
                        -ms-transform: rotate(45deg);
                        transform: rotate(45deg);
                        font-weight: 800;
                      }
                    }

                    .select-all-recipes {
                      display: flex;
                      font-size: 14px;
                      font-weight: 700;
                      color: $black $jet-black;
                      cursor: pointer;
                    }
                  }
                }
              }

              .product-table-srno {
                font-weight: 400;
                color: $black;
                font-size: 14px;
                display: grid;
                align-content: center;
                min-width: 22px;
                margin-left: 15px;

                .select-all-checbox-section {
                  width: 2%;

                  .recipes-selection-checkbox-section {
                    display: block;
                    position: relative;
                    font-size: 22px;
                    -webkit-user-select: none;
                    -moz-user-select: none;
                    -ms-user-select: none;
                    user-select: none;

                    input {
                      opacity: 0;
                      height: 0;
                      width: 0;

                      &:checked {
                        ~ {
                          .checkmark {
                            background-color: $green-light;
                            border: 3px solid $green-light;

                            &:after {
                              display: block;
                            }
                          }
                        }
                      }
                    }

                    .checkmark {
                      position: absolute;
                      top: -3px;
                      left: 0;
                      height: 24px;
                      width: 24px;
                      color: $grainsboro;
                      background-color: $white;
                      border: 3px solid $grainsboro;
                      border-radius: 4px;
                      cursor: pointer;

                      &:hover {
                        border: 3px solid $green-light;
                      }

                      &:after {
                        content: "";
                        position: absolute;
                        display: none;
                        left: 6px;
                        top: 2px;
                        width: 6px;
                        height: 12px;
                        border: solid $white;
                        border-width: 0 2px 2px 0;
                        -webkit-transform: rotate(45deg);
                        -ms-transform: rotate(45deg);
                        transform: rotate(45deg);
                        font-weight: 800;
                      }
                    }
                  }
                }
              }

              .product-table-image-container {
                display: grid;
                align-content: center;

                .product-table-image {
                  width: 60px;
                  min-width: 60px;
                  height: 60px;
                  min-height: 60px;
                  margin: 10px 0px;
                  display: grid;
                  align-content: center;
                  justify-content: center;

                  .product-image {
                    width: auto;
                    height: auto;
                    max-height: 60px;
                    max-width: 60px;
                  }
                }
              }

              .product-name {
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                width: 100%;
                max-height: 4em;
                margin-left: 20px;
                padding: 27px 0;
                line-height: 1em;
                overflow: hidden;
                text-overflow: ellipsis;
                color: $black;
              }

              .product-id {
                min-width: 105px;
                width: 105px;
                font-weight: 400;
                color: $grey;
                font-size: 14px;
                display: grid;
                align-content: center;
                margin-left: 28px;
                padding: 10px 0px;
                word-break: break-all;
              }

              .product-button-container {
                width: 120px;
                min-width: 120px;
                margin-left: 28px;
                display: grid;
                align-content: center;

                #selection-eanabled {
                  visibility: hidden;
                }
              }

              .product-menu {
                padding: 12px 18px;
                display: grid;
                align-content: center;

                #selection-eanabled {
                  visibility: hidden;
                }

                .delete-icon {
                  height: 24px;
                  width: 22px;
                  min-height: 24px;
                  min-width: 22px;
                  cursor: pointer;
                }
              }
            }
          }
        }
      }

      .disable-table {
        opacity: 0.5;
        pointer-events: none;
      }
    }
  }
}

.search-table-heading {
  background-color: $pearl-mist;
  border-radius: 8px 8px 0px 0px;
  margin: 0px 4px;

  .search-table-heading-row {
    display: flex;
    color: $spanish-gray;
    font-size: 14px;
    height: 28px;
    font-family: $font-family-averta;
    line-height: 1;

    .product-table-srno {
      display: grid;
      align-content: center;
      min-width: 22px;
      margin-left: 15px;
    }

    .product-table-image-container {
      display: grid;
      align-content: center;
      min-width: 60px;
    }

    .product-name {
      display: grid;
      align-content: center;
      margin-left: 16px;
      width: 100%;
    }

    .product-id {
      min-width: 105px;
      width: 105px;
      display: grid;
      align-content: center;
      margin-left: 28px;
    }

    .product-button-container {
      width: 120px;
      min-width: 120px;
      margin-left: 28px;
      display: grid;
      align-content: center;
    }

    .product-menu {
      display: grid;
      min-width: 58px;
    }
  }
}

.search-table-body {
  text-align: left;
  margin: 0px 4px;

  .search-table-body-row {
    position: relative;
    display: flex;
    border-top: 1px solid $bright-gray;
    border-bottom: 1px solid $bright-gray;
    border-left: 1px solid $white;
    border-right: 1px solid $white;

    .product-table-srno {
      font-weight: 400;
      color: $black;
      font-size: 14px;
      display: grid;
      align-content: center;
      min-width: 22px;
      margin-left: 15px;
    }

    .product-table-image-container {
      display: grid;
      align-content: center;

      .product-table-image {
        width: 60px;
        min-width: 60px;
        height: 60px;
        min-height: 60px;
        margin: 10px 0px;
        display: grid;
        align-content: center;
        justify-content: center;

        .product-image {
          width: auto;
          height: auto;
          max-height: 60px;
          max-width: 60px;
        }
      }
    }

    .product-name {
      font-weight: 700;
      color: $black;
      font-size: 14px;
      display: grid;
      align-content: center;
      margin-left: 16px;
      width: 100%;
      padding: 26px 0px;
    }

    .product-id {
      min-width: 105px;
      width: 105px;
      font-weight: 400;
      color: $grey;
      font-size: 14px;
      display: grid;
      align-content: center;
      margin-left: 28px;
      padding: 10px 0px;
      word-break: break-all;
    }

    .product-button-container {
      width: 120px;
      min-width: 120px;
      margin-left: 28px;
      display: grid;
      align-content: center;
    }

    .product-menu {
      padding: 12px 18px;
      display: grid;
      align-content: center;
      min-width: 58px;

      .delete-icon {
        height: 24px;
        width: 22px;
        min-height: 24px;
        min-width: 22px;
        cursor: pointer;
      }
    }
  }
}

.no-searched-result-found {
  font-family: $font-family-averta;
  font-weight: 700;
  font-size: 20px;
  color: $shadow-gray;
  margin-top: 20px;
  display: flex;
  justify-content: space-around;
}

.conformation-popup-for-edit-product-matches-modal {
  padding: 10px 20px;
  text-align: center;
  width: 430px;
  font-family: $font-family-averta;

  .content-for-edit-product-modal {
    .conformation-description {
      font-size: 18px;
      text-align: center;
      margin-bottom: 20px;
      font-weight: 700;
    }

    .edit-product-matches-button-container {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.delete-shoppable-review-modal-popup {
  z-index: 999;
  text-align: left;
  display: flex;
  flex-direction: inherit;
  justify-content: space-between;
  align-items: normal;
  margin-top: 30px;
  max-height: 160px;
  padding: 0 14px;
  width: 450px;
  font-family: $font-family-averta;

  .delete-shoppable-review-image {
    img {
      width: 80px;
      margin-bottom: 10px;
    }
  }

  .delete-shoppable-review-content {
    width: 310px;

    .delete-shoppable-review-title {
      position: relative;
      left: -8px;
      color: $black;
      font-weight: bold;
      font-size: 20px;
    }

    .delete-shoppable-review-description {
      position: relative;
      left: -8px;
      color: $grey;
      line-height: 24px;
    }

    .delete-shoppable-review-button-container {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-top: 32px;
      margin-bottom: 8px;

      .delete-shoppable-review-delete-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50px;
        border: 0;
        background-color: $fiery-red-blaze;
        text-shadow: 0 -1px 0 $faint-black;
        color: $white;
        font-weight: 600;
        box-shadow: 0 2px 4px 0 $box-shadow;
        margin-left: 10px;
        text-align: center;
        cursor: pointer;
        width: 121px;
        height: 44px;
        font-size: 16px;
      }

      .delete-shoppable-review-cancel-btn {
        width: 121px;
        height: 44px;
        font-size: 16px;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        color: $green;
        box-shadow: 0 2px 4px 0 $box-shadow;
        cursor: pointer;
        border-radius: 50px;
        border: 1px solid $subtle-whisper-grey;
        background-color: $white;
      }
    }
  }
}

.popup-for-edit-product-cannot-update-recipe {
  padding: 10px 20px;
  text-align: center;
  width: 400px;
  font-family: $font-family-averta;

  .message {
    font-size: 18px;
    text-align: center;
    margin-bottom: 20px;
    font-weight: 600;
  }

  .edit-product-cannot-update-recipe-ok-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 21px;
    border-radius: 20px;
    border: 0;
    background-color: $green;
    text-shadow: 0 -1px 0 $faint-black;
    color: $white;
    font-weight: 700;
    box-shadow: 0 2px 4px 0 $box-shadow;
    margin: 5px;
    text-align: center;
    cursor: pointer;
    min-width: 90px;
    height: 41px;
    font-size: 16px;
  }
}

.container-for-shoppable-review-for-edit-product {
  margin: 0 20px;
  z-index: 11;
  font-family: $font-family-averta;

  .goto-recipe {
    padding: 10px 20px;
    text-align: center;
    width: 430px;

    .goto-recipe-content {
      .confirm-exit-top-section {
        display: flex;
        margin-bottom: 29px;

        .confirm-exit-image {
          min-width: 80px;
        }
      }

      .confirmation-description {
        font-size: 18px;
        text-align: left;
        margin-bottom: 20px;
        margin-left: 20px;
        margin-top: 10px;
        font-weight: 700;
        position: relative;

        .leave-page-warning {
          position: absolute;
          top: 53px;
          left: 0px;
          font-weight: 400;
          font-size: 12px;
          color: $red;
        }
      }

      .edit-description-subtitle {
        font-weight: 400;
        font-size: 16px;
        color: $red;
        margin-top: 10px;
      }

      .organizations-confirmation-button-container {
        display: flex;
        align-items: center;
        justify-content: right;
        gap: 20px;
      }
    }
  }
  .edit-shoppable-review-table {
    display: flex;
    flex-direction: column;
    padding-top: 20px;

    .edit-shoppable-review-table-head {
      .th {
        padding: 8px 20px;
      }

      .edit-shoppable-review-table-head-row {
        display: flex;
        color: $steel-gray;
        text-align: left;
        text-transform: uppercase;
      }
    }

    .edit-shoppable-review-table-body {
      display: flex;
      flex-direction: column;
      border: 1px solid $grainsboro;

      &:hover {
        background: $light-mint;
        border: 1px solid $green;

        .show-more-detail {
          .line {
            background-color: $green;
          }
        }
      }

      .td {
        padding: 0px 24px 4px 20px;
      }

      .edit-shoppable-review-table-body-row {
        display: flex;
        color: $black;
        text-align: left;

        .edit-shoppable-notes-data {
          position: relative;
        }

        .filter-select-input {
          border: none;
          background-color: $transparent;
          color: $black;
          height: 37px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          position: relative;
        }

        .ingredientShoppable {
          margin-top: 10px;
        }

        .edit-shoppable-ingredient-data {
          color: $black;
          position: relative;
        }

        .editshoppable-notes-data {
          color: $black;
        }

        .edit-shoppable-quantity-data {
          color: $black;
        }

        .edit-quantity-uom-data {
          position: relative;
          color: $black;

          .dropdown-quantity-icon {
            position: absolute;
            top: 15px;
            left: 0px;
            width: 6px;
            transform: rotate(90deg);
            cursor: pointer;

            &.dropdown-disabled {
              cursor: default;
              pointer-events: none;
            }
          }

          .autocomplete-results-uom {
            position: absolute;
            top: 33px;
            left: 0px;
            right: 5px;
            z-index: 2;
            width: 100%;
            max-height: 134px;
            padding: 4px;
            border-radius: 4px;
            box-shadow: 0 1px 10px 0 $box-shadow;
            background: $white;
            color: $charcoal-light;
            list-style: none;
            overflow-y: scroll;
            scrollbar-width: none;
            &::-webkit-scrollbar {
                display: none;
            }
          }
          .autocomplete-result-uom {
            padding: 8px 7px;
            cursor: pointer;
            margin: 2px;
            border-radius: 4px;

            &.is-active,
            &:hover {
              background: $green;
              color: $white;
            }
          }
        }

        .edit-shoppable-data {
          position: relative;
          color: $jet-black;

          span {
            color: $gunmetal-grey;
          }

          .dropdown-shoppable-data {
            position: absolute;
            top: 15px;
            left: 0px;
            width: 6px;
            transform: rotate(90deg);
            cursor: pointer;

            &.dropdown-disabled {
              cursor: default;
              pointer-events: none;
            }
          }

          .edit-autocomplete-results-shoppable {
            position: absolute;
            top: 33px;
            left: 0px;
            right: 5px;
            z-index: 1;
            width: 80%;
            max-height: 360px;
            padding: 4px;
            border-radius: 4px;
            box-shadow: 0 1px 10px 0 $box-shadow;
            overflow-y: scroll;
            scrollbar-width: none;
            background: $white;
            color: $charcoal-light;
            &::-webkit-scrollbar {
              display: none;
            }
          }

          .autocomplete-result-shoppable {
            padding: 8px 7px;
            cursor: pointer;
            margin: 2px;
            border-radius: 4px;

            &.is-active,
            &:hover {
              background: $green;
              color: $white;
            }
          }
        }

        .shoppable-quantity-data {
          position: relative;
        }
      }

      .text-light-h3{
        margin-bottom: 0px;
      }

      .show-more-detail {
        padding-right: 24px;
        padding-left: 20px;
        font-size: 14px;

        .line {
          height: 1px;
          width: 100%;
          background-color: $grainsboro;
          margin-top: 6px;
        }

        .computed-size {
          position: relative;
          top: 10px;
          width: 27%;
          padding-right: -8px;
          display: flex;
          .computed-size-text {
            display: flex;
            width: max-content;
            padding-right: 6px;
            color: $jet-black;
            font-family: $font-family-averta;
          }
          .computed-size-no-data {
            color: $gunmetal-grey;
            line-height: 18px;
            font-style: normal;
          }

          .computed-size-with-data {
            display: flex;
            width: 35%;
            color: $jet-black;
            line-height: 18px;
            font-style: normal;
          }

          .computed-unit {
            margin-left: 1px;
          }
        }
      }

      .dropdown-icon-show-more-detail {
        width: 19px;
        transform: rotate(90deg);
        margin: 0 auto;
        margin-bottom: 4px;
        cursor: pointer;
      }
    }
  }

  .not-shoppable-products {
    text-align: left;
    margin: 20px 0px;

    .not-shoppable-text {
      color: $red-orange;
    }
  }

  .non-products {
    display: flex;
    justify-content: center;
    padding-top: 25px;

    .text {
      color: $gunmetal-grey;
    }
  }

  .main-product-container {
    padding: 0px 20px;
  }

  .promoted-text-heading {
    display: flex;
    padding: 20px 0px;
    gap: 15px;

    .promoted-products {
      font-size: 16px;
      font-weight: 600;
      color: $jet-black;
      border-radius: 12px;
    }

    .tooltip-zero-promoted-message {
      position: relative;

      .tooltip-icon {
        height: 16px;
        width: 16px;
        cursor: pointer;

        img {
          height: 100%;
          width: 100%;
        }
      }
    }
  }

}

.edit-ingredients-name-container {
  padding: 12px 22px;
  font-family: $font-family-averta;
  width: 830px;

  .ingredient-already-exit-message {
    font-weight: 400;
    font-size: 12px;
    color: $fiery-red-blaze;
    text-align: left;
    width: 410px;
    margin: 0 22px;
  }

  .input-container {
    display: grid;
    padding: 4px 20px;

    .ing-note-heading {
      display: flex;
      font-size: 16px;
      margin: 1px;
      margin-bottom: 8px;
      color: $grey;
      justify-content: center;
      margin-right: 139px;
    }

    .edit-ing-headers {
      display: grid;
      grid-template-columns: 1fr 1fr;

      .ing-input-heading {
        display: flex;
        font-weight: 700;
        font-size: 16px;
        margin: 1px;
        max-width: 378px;
        overflow-wrap: anywhere;
        text-align: left;
      }
    }

    .singular-name-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      column-gap: 26px;
      margin-top: 8px;

      .singular-loader-container {
        position: relative;

        .input-loading {
          position: absolute;
          z-index: 9;
          height: 50px;
          right: 40px;
          right: 13px;
          bottom: 31px;

          .loader-image {
            position: absolute;
            border: 3px solid $white;
            border-radius: 50%;
            border-top: 3px solid $green;
            border-right: 3px solid $green;
            border-bottom: 3px solid $green;
            width: 22px;
            height: 22px;
            -webkit-animation: spin 2s linear infinite;
            animation: spin 2s linear infinite;
            right: 0px;
            top: 44px;
          }
        }
      }

      .singular-ingredient-note-container {
        position: relative;

        .ing-note {
          position: absolute;
          bottom: 58px;
          left: 4px;
          font-size: 16px;
          color: $grey;
        }
      }

      .edit-ingredient-input {
        width: 410px;
        height: 50px;
        font-size: 16px;
        color: $black;
        border-radius: 4px;
        background-color: $pearl-mist;
        border: 1px solid $grainsboro;
        padding: 1px 36px 1px 14px;
      }

      .edit-ingredient-note {
        width: 320px;
        height: 50px;
        font-size: 16px;
        color: $black;
        border-radius: 4px;
        background-color: $pearl-mist;
        border: 1px solid $grainsboro;
        padding: 1px 14px;
      }
    }

    .plural-name-container {
      .plural-loader-container {
        .input-loading {
          position: absolute;
          z-index: 9;
          height: 50px;
          right: 40px;
          right: 13px;
          bottom: 31px;

          .loader-image {
            position: absolute;
            border: 3px solid $white;
            border-radius: 50%;
            border-top: 3px solid $green;
            border-right: 3px solid $green;
            border-bottom: 3px solid $green;
            width: 22px;
            height: 22px;
            -webkit-animation: spin 2s linear infinite;
            animation: spin 2s linear infinite;
            right: 8px;
            top: 44px;
          }

          .loader-imageplural {
            position: absolute;
            border: 3px solid $white;
            border-radius: 50%;
            border-top: 3px solid $green;
            border-right: 3px solid $green;
            border-bottom: 3px solid $green;
            width: 22px;
            height: 22px;
            -webkit-animation: spin 2s linear infinite;
            animation: spin 2s linear infinite;
            right: 0px;
            top: 44px;
          }
        }
      }

      .plural-ingredient-note-container {
        position: relative;

        .ing-noted {
          position: absolute;
          bottom: 57px;
          left: 4px;
          color: $grey;
          font-size: 16px;
        }
      }

      .edit-ingredient-input {
        width: 410px;
        height: 50px;
        font-size: 16px;
        color: $black;
        border-radius: 4px;
        background-color: $pearl-mist;
        border: 1px solid $grainsboro;
        padding: 1px 36px 1px 14px;
      }

      .edit-ingredient-note {
        width: 320px;
        height: 50px;
        font-size: 16px;
        color: $black;
        border-radius: 4px;
        background-color: $pearl-mist;
        border: 1px solid $grainsboro;
        padding: 1px 14px;
      }
    }

    .ingredient-name-section {
      width: 60%;
      text-align: left;

      .ing-input-heading-plural {
        display: flex;
        font-weight: 700;
        font-size: 16px;
        margin: 1px;
        margin-bottom: 8px;
        margin-top: 22px;
      }
    }

    .loader-section {
      .input-loading {
        display: flex;
        align-items: center;
        position: relative;
        z-index: 9;
        height: 50px;
        right: 40px;

        .loader-image {
          position: absolute;
          border: 3px solid $white;
          border-radius: 50%;
          border-top: 3px solid $green;
          border-right: 3px solid $green;
          border-bottom: 3px solid $green;
          width: 22px;
          height: 22px;
          -webkit-animation: spin 2s linear infinite;
          animation: spin 2s linear infinite;
          right: 8px;
          top: 44px;
        }

        .loader-imageplural {
          position: absolute;
          border: 3px solid $white;
          border-radius: 50%;
          border-top: 3px solid $green;
          border-right: 3px solid $green;
          border-bottom: 3px solid $green;
          width: 22px;
          height: 22px;
          -webkit-animation: spin 2s linear infinite;
          animation: spin 2s linear infinite;
          right: 8px;
          top: 145px;
        }
      }
    }

    .ingredient-notes-section {
      width: 40%;

      .ing-note-heading {
        display: flex;
        font-size: 16px;
        margin: 1px;
        margin-bottom: 8px;
        color: $grey;
      }

      .ing-note-heading-plural {
        display: flex;
        font-size: 16px;
        margin: 1px;
        margin-bottom: 8px;
        margin-top: 22px;
        color: $grey;
      }
    }
  }

  .about-notes-cont {
    display: flex;
    justify-content: space-between;
    margin: 12px 0;

    .about-notes {
      width: 320px;
      height: 36px;
      font-weight: 400;
      font-size: 12px;
      text-align-last: left;
      margin-bottom: 3px;
    }
  }

  .table-image-loader {
    display: flex;
    justify-content: center;
    z-index: 9;
    margin: 0 auto;
    height: 200px;
    width: 210px;

    .loader-ingredient-text {
      width: 468px;
      background: $green-peppermint;
      text-align: center;
      position: absolute;
      top: 55%;
      font-size: 16px;
      color: $black;
      border-radius: 8px;
      border: 1px solid $green;
      padding: 18px 0px;
    }

    .loader {
      top: 26%;
      position: relative;
      border: 3px solid $pristine-white;
      border-radius: 50%;
      border-top: 3px solid $green;
      border-right: 3px solid $green;
      border-bottom: 3px solid $green;
      width: 24px;
      height: 24px;
      -webkit-animation: spin 2s linear infinite;
      animation: spin 2s linear infinite;
    }

    @-webkit-keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
      }

      100% {
        -webkit-transform: rotate(360deg);
      }
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }
  }

  .edit-ingredients-name-top-part {
    position: relative;
    margin-bottom: 24px;

    .edit-ingredient-name-heading {
      font-weight: 700;
      font-size: 24px;
      color: $black;
      text-align: left;
      margin-left: 20px;
    }

    .edit-ingredients-name-close-modal {
      position: absolute;
      width: 24px;
      height: 24px;
      right: 0px;
      top: 6px;
      cursor: pointer;
    }
  }

  .edit-ingredients-name-main-container {
    .edit-ingredients-name {
      font-weight: 400;
      font-size: 16px;

      .edit-ingredients-name-title {
        color: $grey;
        text-align: left;
        margin-bottom: 9px;
      }

      .input-section-ingredient {
        border: 1px solid $grainsboro;
        border-radius: 4px;
        width: 100%;
        height: 50px;
        margin-bottom: 12px;
        padding: 15px 49px 15px 15px;
        caret-color: $shadow-gray;
      }

      #ingredientsDataId {
        padding-right: 60px;
      }

      .input-section {
        display: flex;
      }
    }

    .edit-ingredients-name-necessary-message {
      font-weight: 400;
      font-size: 12px;
      color: $black;
      text-align: left;
      margin-bottom: 16px;
    }
  }

  .edit-ingredients-name-button-container {
    display: flex;
    justify-content: flex-end;
    gap: 20px;
  }
}

.shoppable-preview-reference-ingredient-container {
  height: 404px;
  width: 800px;
  padding: 15px 20px;
  font-family: $font-family-averta;

  .shoppable-preview-reference-ingredient-top-section {
    display: flex;
    justify-content: space-between;

    .reference-product-section-name {
      color: $black;
    }

    .reference-product-section-close-button {
      height: 24px;
      width: 24px;
      cursor: pointer;

      img {
        height: 100%;
        width: 100%;
      }
    }
  }

  .shoppable-preview-reference-ingredient-middle-section {
    margin-top: 10px;
    display: flex;

    .shoppable-preview-reference-ingredient-name {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      color: $grey;
    }
  }

  .shoppable-preview-reference-ingredient-bottom-section {
    margin-top: 15px;
    position: relative;

    .shoppable-preview-reference-ingredient-table-data {
      position: relative;
      width: 100%;
      border: 1px solid $grainsboro;
      border-radius: 8px;
      text-align: left !important;
      margin-bottom: 24px;

      .reference-preview-table {
        .reference-ingredient-table-head {
          .reference-ingredient-table-head-row {
            .ingredient-image-heading {
              width: 15%;
            }

            .ingredient-product-id-heading {
              width: 15%;
            }

            .ingredient-product-title-heading {
              width: 30%;
            }

            .ingredient-product-weight-heading {
              width: 10%;
              text-align: center;
            }

            .ingredient-product-quantity-heading {
              width: 10%;
              text-align: center;
            }

            .ingredient-product-store-count-heading {
              width: 16%;
              text-align: center;
            }

            th {
              text-align: left;
              text-transform: uppercase;
              padding: 8px 0px;
              background: $white-smoke;
              font-size: 12px;
              line-height: 1.3333;
              font-weight: 700;
              color: $spanish-gray;
            }

            th:first-child {
              border-top-left-radius: 8px;
            }

            th:last-child {
              border-top-right-radius: 8px;
            }
          }
        }

        .reference-ingredient-table-body {
          .reference-ingredient-table-body-row {
            td {
              padding: 18px 0px;
              text-align: left;
            }

            .ingredient-image {
              text-align: center;

              img {
                object-fit: contain;
                height: 60px;
                width: 60px;
              }
            }

            .ingredient-product-id {
              color: $gunmetal-grey;
              line-height: 1.5;
            }

            .ingredient-product-title {
              color: $jet-black;
              line-height: 1.5;

              .product-title {
                width: 90%;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }
            }

            .ingredient-product-weight {
              color: $gunmetal-grey;
              line-height: 1.5;
              text-align: center;
            }

            .ingredient-product-quantity {
              color: $gunmetal-grey;
              line-height: 1.5;
              text-align: center;
            }

            .ingredient-product-store-count {
              color: $gunmetal-grey;
              line-height: 1.5;
              text-align: center;
            }

            &:last-child {
              td {
                border-bottom: unset;
              }

              td:first-child {
                border-bottom-left-radius: 8px;
              }

              td:last-child {
                border-bottom-right-radius: 8px;
              }
            }
          }
        }
      }

      .reference-tags-main-container {
        background-color: $pearl-mist;
        padding: 15px 10px;
        margin-top: 7px;

        .reference-ingredient-tags-heading {
          font-weight: 700;
          color: $jet-black;
          font-size: 16px;
        }

        .reference-tags-list-container {
          display: flex;
          flex-wrap: wrap;

          .ingredient-reference-tags-card {
            display: flex;
            color: $jet-black;
            border: 1px solid $grainsboro;
            background-color: $white;
            padding: 6px;
            border-radius: 4px;
            margin-top: 5px;
            margin-right: 8px;

            .ingredient-reference-tags-image {
              height: 18px;
              width: 18px;
            }

            .ingredient-reference-tags-name {
              font-family: $font-family-arial-serif;
              line-height: 1;
            }
          }
        }
      }
    }

    .shoppable-preview-reference-ingredient-cancel-button {
      display: flex;
      flex-direction: row-reverse;
      padding-bottom: 20px;
    }
  }
}

  .shoppable-product-copy-to-clipboard-information {
    display: flex;
    justify-content: center;

    .copy-to-clipboard-successfully {
      position: absolute;
      font-weight: 600;
      font-size: 16px;
      line-height: 24px;
      color: $charcoal-gray;
      background-color: $green-peppermint;
      border-radius: 7px;
      border: 1px solid $green-light;
      padding: 10px 15px;
      top: 15px;
      z-index: 999;

      .green-correct {
        height: 22px;
        width: 22px;
        position: relative;
        top: -2px;
        margin-right: 8px;
      }

      .close-icon {
        margin-left: 15px;
        height: 16px;
        width: 16px;
        position: relative;
        bottom: 1px;
        cursor: pointer;
      }
    }
  }

  .shoppable-review-popup-main-navigation {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    margin-bottom: 20px;

    .shoppable-review-popup-main-heading {
      width: 236px;
      height: 29px;
      font-weight: 700;
      font-size: 26px;
    }

    .close-image {
      cursor: pointer;
      width: 24px;
      height: 24px;
    }
  }

  .shoppable-review-for-edit-product-main::-webkit-scrollbar {
    width: 12px;
    border-radius: 3px;
  }

  .shoppable-review-for-edit-product-main::-webkit-scrollbar-track {
    background: $whispering-white-smoke;
  }

  .shoppable-review-for-edit-product-main::-webkit-scrollbar-thumb {
    background: $grainsboro;
    border: 3px solid $transparent;
    border-radius: 15px;
    background-clip: content-box;
  }

  .shoppable-review-for-edit-product-main {
    height: 54vh;
    width: 102%;
    overflow-y: scroll;
    scrollbar-color: $grainsboro $whispering-white-smoke;
    scrollbar-width: thin;

    @media screen and (min-width: 1400px) {
      height: 57vh;
    }

    @media screen and (min-width: 1600px) {
      height: 64vh;
    }

    @media screen and (min-width: 1900px) {
      height: 67vh;
    }
  }

  .Shoppable-popup-loader-section {
    .content {
      width: 100%;
      height: 100%;
      background-color: $white;
      border-radius: 8px;
      position: relative;

      .loading {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        min-height: 255px;
        padding: 0 20px;

        .input-loading {
          height: 60px;
          display: flex;
          justify-content: center;

          .loader-image {
            border: 3px solid $white;
            border-radius: 50%;
            border-top: 3px solid $green;
            border-right: 3px solid $green;
            border-bottom: 3px solid $green;
            width: 20px;
            height: 20px;
            -webkit-animation: spin 2s linear infinite;
            animation: spin 2s linear infinite;
          }
        }

        .loading-text {
          background-color: $green-peppermint;
          border-radius: 4px;
          border: 1px solid $green-fringy-flower;
          width: 468px;
          height: 57px;

          p {
            font-weight: 400;
            font-size: 16px;
            color: $shadow-gray;
            padding: 18px 0px;
            text-align: center;
          }
        }
      }
    }
  }

  .zero-promoted-product-line {
    height: 1px;
    width: 100%;
    background: $grainsboro;
  }

  .product-section {
    margin-top: 10px;
  }

  .shoppable-review-for-edit-product {
    padding: 28px;
    padding-top: 23px;
    width: 82vw;
    border: 1px solid $grainsboro;
    border-radius: 8px;
    background-color: $white;

    .shoppable-review-popup-main {
      .shoppable-review-popup-content {
        .shoppable-review-popup-content-dashboard {
          display: flex;
          justify-content: space-between;

          .shoppable-review-popup-content-details {
            .shoppable-review-popup-content-image {
              object-fit: cover;
              width: 80px;
              border-radius: 4px;
              margin-top: 4px;
            }

            .about-image {
              text-align: left;
              margin-left: 10px;
              max-width: 650px;
              .goto-recipe-link {
                  color: $navy-blue;
                &:hover {
                  text-decoration: underline;
                }
              }
              .shoppable-review-popup-content-number {
                height: 20px;
                font-weight: 400px;
                font-size: 12px;
                color: $pebble-gray;
                margin-top: 3px;
              }

              .shoppable-review-popup-title {
                font-weight: 700;
                font-size: 16px;
                line-height: 1;
                color: $black;
                height: auto;
                margin-bottom: 6px;
              }

              .shoppable-review-popup-subtitle {
                height: auto;
                font-weight: 400px;
                font-size: 14px;
                color: $black;
                margin-bottom: 4px;
              }
            }
          }

          .shoppable-review-popup-content-details-selectbox {
            margin: 18px 14px;
            margin-right: 0px;

            .quantity-product-section {
              display: flex;

              .quantity-product-text {
                position: relative;
                right: 20px;
                font-size: 16px;
                font-weight: 700;
                line-height: 20px;
                color: $jet-black;
              }

              .quantity-product-number-section {
                position: relative;
                bottom: 15px;
                display: flex;
                border: 1px solid $grainsboro;
                background: $white;
                border-radius: 4px;
                width: 65px;
                height: 50px;
                justify-content: space-around;
                align-items: center;

                .autocomplete-results {
                  position: absolute;
                  list-style: none;
                  top: 45px;
                  left: 0px;
                  right: 5px;
                  box-shadow: 0 1px 10px 0 $box-shadow;
                  background: $white;
                  z-index: 1;
                  color: $charcoal-light;
                  max-height: 360px;
                  overflow-y: scroll;
                  width: 100%;
                  border-radius: 4px;
                  scrollbar-width: none;

                  &::-webkit-scrollbar {
                    display: none;
                  }
                }

                .autocomplete-result {
                  padding: 8px 20px;
                  cursor: pointer;
                  margin: 2px;
                  border-radius: 4px;

                  &.is-active,
                  &:hover {
                    background: $green;
                    color: $white;
                  }
                }

                .quantity-product-number {
                  font-size: 16px;
                  font-weight: 400;
                  color: $black;
                }

                .quantity-product-dropdown {
                  transform: rotate(90deg);
                  width: 6px;
                  height: 10px;
                  cursor: pointer;

                  &.dropdown-disabled {
                    cursor: default;
                    pointer-events: none;
                  }
                }
              }
            }
          }
        }
      }
    }

    .shoppable-reference-product-container {
      display: flex;
      margin-top: 15px;

      .shoppable-reference-product-text-clipboard {
        display: flex;
        align-items: center;

        .shoppable-reference-product-section {
          position: relative;

          .shoppable-underline-reference-product-text {
            display: flex;
            color: $green-light;
            cursor: pointer;
          }

          .shoppable-underline-reference-no-product-text {
            display: flex;
            color: $green-light;
          }

          .shoppable-reference-product-preview-section {
            visibility: hidden;
            position: absolute;
            left: 170px;
            top: -90px;
            z-index: 999;
            height: 210px;
            width: 200px;
            background: $black;
            opacity: 0.8;
            border-radius: 10px;

            &:before {
              position: absolute;
              transform: rotate(90deg);
              content: "";
              border-width: 10px;
              border-style: solid;
              border-color: $black $transparent $transparent $transparent;
              top: 86px;
              left: -18px;
            }

            .shoppable-reference-product-data-section {
              .shoppable-reference-top-section {
                display: flex;
                justify-content: center;
                margin: 15px 8px 0px 8px;

                .shoppable-reference-product-image {
                  display: flex;
                  height: 90px;
                  width: 90px;
                  margin-left: 8px;
                  justify-content: center;

                  img {
                    border-radius: 8px;
                    object-fit: contain;
                  }
                }
              }

              .shoppable-reference-bottom-section {
                margin-top: 10px;
                padding: 8px;

                .shoppable-reference-product-name {
                  line-height: 1.5;
                  color: $white;
                  text-align: center;
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;
                  overflow: hidden;
                }

                .shoppable-reference-product-info {
                  margin-top: 2px;
                  line-height: 1.5;
                  color: $white;
                  text-align: center;
                }
              }
            }
          }

          &:hover .shoppable-reference-product-preview-section {
            visibility: visible;
          }
        }

        .shoppable-reference-product-copy-clipboard {
          position: relative;
          margin-left: 12px;

          .clipboard-image {
            position: relative;
            bottom: 3px;
            cursor: pointer;
            height: 20px;
            width: 18px;

            img {
              height: 100%;
              width: 100%;
            }
          }
        }
      }
    }

    .edit-shoppable-promoted-products {
      padding-top: 25px;
      padding-bottom: 10px;
      display: flex;
      justify-content: space-between;

      .promoted-products {
        font-size: 16px;
        font-weight: 600;
        color: $jet-black;
      }

      .disable-toggle {
        pointer-events: none;
        opacity: 0.5;
      }

      .show-only-promoted-products {
        color: $orange;

        .toggle-top {
          vertical-align: middle;
        }

        .switch {
          position: relative;
          display: inline-block;
          width: 42px;
          height: 26px;
          margin-left: 20px;

          input {
            opacity: 0;
            width: 0;
            height: 0;
          }
        }

        .slider-round {
          position: absolute;
          cursor: pointer;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: $light-white;
          -webkit-transition: 0.4s;
          transition: 0.4s;
          border-radius: 30px;

          &:before {
            position: absolute;
            content: "";
            height: 23px;
            width: 23px;
            left: 2px;
            bottom: 2px;
            background-color: $white;
            -webkit-transition: 0.4s;
            transition: 0.4s;
            border-radius: 50%;
          }
        }

        input {
          &:checked {
            + {
              .slider-round {
                background-color: $green-light;
                &:before {
                  -webkit-transform: translateX(15px);
                  -ms-transform: translateX(15px);
                  transform: translateX(15px);
                }
              }
            }
          }

          &:focus {
            + {
              .slider-round {
                box-shadow: 0 0 1px $green;
              }
            }
          }
        }
      }

      .tag-filter-section {
        position: relative;
        display: flex;
        color: $copper-rust;
        justify-content: space-between;

        .tag-filter {
          font-size: 14px;
          margin-right: 10px;
          margin-top: -11px;
          display: flex;
          font-weight: 700;
          text-transform: uppercase;

          .tag-filter-text {
            text-align: center;
            margin-top: 13px;
            margin-right: 10px;
          }
        }
        .shoppable-search-brand-main {
          position: absolute;
          top: 50px;
          z-index: 9;
          width: 414px;
          height: auto;
          overflow-y: visible;
          background-color: $white;
          border: 2px solid $grainsboro;
          border-bottom: 16px solid $grainsboro;
          border-radius: 4px;
          box-shadow: 0 1px 10px 0 $shadow-black;
          scrollbar-color: $grainsboro $whispering-white-smoke;
          scrollbar-width: thin;

          ::-webkit-scrollbar {
            width: 12px;
            border-radius: 3px;
          }

          ::-webkit-scrollbar-track {
            background: $whispering-white-smoke;
          }

          ::-webkit-scrollbar-thumb {
            background: $grainsboro;
            border: 3px solid $transparent;
            border-radius: 15px;
            background-clip: content-box;
          }

          .shoppable-brand-search-bar {
            position: static;
            height: 70px;
            background-color: $white;

            .shoppable-search-bar-content {
              position: relative;
              top: 15px;
              left: 30px;
              width: 342px;
              height: 36px;
              margin-bottom: -40px;
              padding: 0 16px 0 16px;
              background-color: $pristine-white;
              border: 1px solid $grainsboro-gray;
              border-radius: 30px;

              .shoppable-search-icon-grey-image {
                position: absolute;
                top: 9px;
                left: 14px;
                width: 20px;
                height: 18px;
                cursor: pointer;
              }

              .shoppable-search-bar-text {
                position: relative;
                left: 10px;
                width: 296px;
                top: 7px;
                background: none;
                color: $graphite-gray;
                caret-color: $green;
                border: none;
                border-radius: 0;
                box-shadow: none;
              }

              .shoppable-align-search-input-box {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .shoppable-exit-search-icon {
                position: absolute;
                top: 11px;
                right: 8px;
                width: 14px;
                height: 14px;
                cursor: pointer;
                display: none;
              }
            }
          }

          .shoppable-search-brand-data-main {
            overflow-y: auto;
            max-height: 206px;

            .shoppable-image-loader {
              display: flex;
              justify-content: center;
              align-items: center;
              height: 200px;
              width: 210px;
              margin: 0 auto;
              background-color: $white;
              z-index: 9;

              .shoppable-loader {
                width: 24px;
                height: 24px;
                border: 3px solid $pristine-white;
                border-radius: 50%;
                -webkit-animation: spin 2s linear infinite;
                animation: spin 2s linear infinite;
              }

              @-webkit-keyframes spin {
                0% {
                  -webkit-transform: rotate(0deg);
                }

                100% {
                  -webkit-transform: rotate(360deg);
                }
              }

              @keyframes spin {
                0% {
                  transform: rotate(0deg);
                }

                100% {
                  transform: rotate(360deg);
                }
              }
            }

            .shoppable-brand-details-checkbox {
              width: 100%;
              cursor: pointer;

              .shoppable-brand-data {
                display: flex;
                width: 100%;
                padding: 12px;

                &:hover {
                  background: $aqua-spring !important;
                }

                .filter-checked-box {
                  position: relative;
                  width: 24px;
                  height: 24px;
                  margin-left: 20px;
                  border: 2px solid $green-light;
                  border-radius: 4px;
                  background: $green-light;
                }

                .filter-checked-box::before {
                  content: "";
                  position: absolute;
                  top: 0;
                  left: 7px;
                  width: 8px;
                  height: 16px;
                  border: 2px solid $white;
                  border-width: 0 2px 2px 0;
                  transform: rotate(45deg);
                }

                .filter-without-checked-box {
                  position: relative;
                  width: 24px;
                  height: 24px;
                  margin-left: 20px;
                  border: 2px solid $grainsboro;
                  border-radius: 4px;
                  background: $white;
                }

                .shoppable-search-list-data {
                  display: flex;
                  position: relative;
                  top: 2px;
                  left: 16px;

                  .search-shoppable-sort-name {
                    color: $black;
                  }
                }
              }

              .shoppable-brand-data-add-background {
                background-color: $aqua-spring;
              }
              .is-active {
                background-color: $aqua-spring;
              }
            }
          }

          .no-result-for-shoppable {
            display: flex;
            justify-content: space-around;
            margin-top: 14px;
            font-weight: 700;
            font-size: 16px;
            color: $shadow-gray;
          }

          .shoppable-apply-btn-container {
            position: relative;
            top: 14px;

            .shoppable-filter-save-btn {
              display: flex;
              justify-content: center;
              position: sticky;
              background-color: $white;
              width: 410px;
              height: 50px;

              .shoppable-filter-btn {
                width: 175px;
                height: 35px;
                margin: 0 0 10px 0;
                background: $green-light;
                border: none;
                border-radius: 20px;
                text-transform: uppercase;
                color: $white;
                cursor: pointer;
              }
            }
          }
        }

        .tag-search-brand-popup {
          height: 44px;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .tag-brand-details {
          display: flex;
          border: 1px solid $grainsboro;
          border-radius: 22px;
          width: auto;
          justify-content: center;
          align-items: center;
          font-size: 14px;
          padding: 7px 20px;
          cursor: pointer;

          .tag-brand-title {
            font-size: 16px;
            margin-left: 0px;
          }

          .tag-brand-selected {
            margin: 0 8px;
            color: $copper-rust;
            font-size: 16px;
            text-transform: capitalize;
          }

          .tag-brand-dropdown-icon {
            width: 20px;
            height: 20px;
            cursor: pointer;
          }

          .tag-brand-space-icon {
            margin-right: 20px;
          }
        }

        .tag-line {
          height: 30px;
          width: 1px;
          background-color: $bright-gray;
          position: relative;
          left: -46px;
          margin: 0 11px;
        }

        .tag-exit-box-section {
          position: relative;
          right: 58px;
          width: 33px;
          height: 100%;
        }

        .tag-exit-brand-icon {
          height: 15px;
          width: 15px;
          top: 12px;
          position: relative;
          left: 0px !important;
          cursor: pointer;
        }
      }
    }

    .edit-product-matches-section {
      .product-matches-title-search {
        display: flex;

        .dynamic-title {
          margin: 20px 0px 10px 0px;
        }

        .dynamic-no-product-title {
          margin: 25px 0px 0px 0px !important;
        }

        .product-matches-title {
          font-weight: 600;
          font-size: 16px;
          color: $jet-black;
          text-align: left;
          padding-bottom: 10px;
          display: flex;
          align-items: center;
          gap: 12px;

          .not-applicable {
            color: $fiery-red-blaze;
            font-size: 14px;
            font-weight: 400;
          }

          .search-results-from-all-product-matches {
            font-weight: 400;
            font-size: 14px;
            color: $grey;
            padding-top: 4px;
          }

          .tag-filter-zero-product-tooltip-section {
            position: relative;
            cursor: pointer;

            .tooltip-icon {
              height: 16px;
              width: 16px;
              cursor: pointer;
              margin-bottom: 5px;

              img {
                height: 100%;
                width: 100%;
              }
            }
          }
        }
      }

      .zero-product-matches-line {
        width: 100%;
        height: 1px;
        background: $grainsboro;
      }

      .shoppable-filter-section {
        margin-top: 10px;
        position: relative;
        display: flex;
        margin-bottom: 10px;
        color: $slate-gray;
        justify-content: space-between;

        .shoppable-filter {
          font-size: 14px;
          margin-right: 10px;
          padding-top: 3px;
          display: flex;
          font-weight: 700;
          text-transform: uppercase;

          .shoppable-filter-text {
            text-align: center;
            margin-top: 13px;
            margin-right: 10px;
          }
        }

        .shoppable-search-brand-popup {
          height: 44px;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .shoppable-brand-details {
          display: flex;
          border: 1px solid $grainsboro;
          border-radius: 22px;
          width: auto;
          justify-content: center;
          align-items: center;
          font-size: 14px;
          padding: 7px 20px;
          cursor: pointer;

          .shoppable-brand-title {
            font-size: 16px;
            margin-left: 0px;
          }

          .shoppable-brand-selected {
            margin: 0 8px;
            color: $green-light;
            font-size: 16px;
          }

          .shoppable-brand-dropdown-icon {
            transform: rotate(90deg);
            width: 20px;
            height: 20px;
            cursor: pointer;
            margin-right: 7px;
          }
        }

        .shoppable-line {
          height: 30px;
          width: 1px;
          background-color: $bright-gray;
          position: relative;
          left: -46px;
          margin: 0 11px;
        }

        .shoppable-exit-box-section {
          position: relative;
          right: 58px;
          width: 33px;
          height: 100%;
        }

        .shoppable-exit-brand-icon {
          height: 15px;
          width: 15px;
          top: 10px;
          position: relative;
          left: 0px !important;
          cursor: pointer;
        }
      }

      .product-matches-search {
        display: flex;
        margin: 10px 10px 30px 10px;

        .search-box-shoppable-review {
          float: right;
          background-color: $pristine-white;
          border: 1px solid $grainsboro-gray;
          border-radius: 30px;
          padding: 0 12px 0 16px;
          height: 46px;
          width: 345px;

          .shoppable-review-input-box {
            width: 320px;
            height: 40px;
            margin: 2px 0px 2px 2px;
            padding: 2px 0px 2px 30px;
            background: none;
            color: $spanish-gray;
            caret-color: $green;
            border: none;
            border-radius: 0;
            box-shadow: none;
          }

          .align-search-input-box {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 293px;
            display: block;
          }

          .exit-shoppable-review {
            width: 18px;
            height: 18px;
            position: relative;
            bottom: 32px;
            float: right;
            cursor: pointer;
          }

          .shoppable-review-icon-green-edit-product-image {
            position: relative;
            top: -33px;
            float: left;
            height: 24px;
            cursor: pointer;
            text-align: right;
            vertical-align: middle;
          }
        }

        .shoppable-review-select-button {
          justify-content: end;
          margin-top: 15px;
          margin-left: 15px;
          font-weight: 700;
          color: $green-light;
          font-size: 14px;
          margin-right: 30px;

          span:hover {
            cursor: pointer;
          }
        }
      }

      .filter-section-no-sort {
        display: flex;
        justify-content: flex-end !important;
      }

      .sort-by-section-main-container-popup {
        text-align: right;
        margin-bottom: 15px;

        .sort-by-section-main-popup {
          display: inline-flex;

          .sort-by-heading-popup {
            line-height: 20px;
            text-transform: uppercase;
            color: $slate-gray;
            align-self: center;
            margin-right: 6px;
          }

          .sort-by-result-main-popup {
            position: relative;
            border: 1px solid $grainsboro;
            border-radius: 4px;
            padding: 12px;
            width: 213px;
            height: 44px;
            cursor: pointer;

            .sort-by-result-text-popup {
              display: flex;
              justify-content: space-between;
              line-height: 16px;
              color: $jet-black;

              .sort-by-arrow-icon-popup {
                .sort-by-dropdown-icon-open-popup {
                  height: 15px;
                  width: auto;
                  transform: rotate(270deg);
                }

                .sort-by-dropdown-icon-close-popup {
                  height: 15px;
                  width: auto;
                  transform: rotate(90deg);
                }
              }
            }

            .sort-by-dropdown-result-popup {
              margin-top: 16px;
              right: 10px;
              width: 210px;
              position: relative;
              box-shadow: 0 1px 10px 0 $box-shadow;
              background: $white;
              z-index: 1;
              color: $charcoal-light;
              overflow-y: scroll;
              border-radius: 4px;
              scrollbar-width: none;

              &::-webkit-scrollbar {
                display: none;
              }

              .sort-by-result-main-container-popup {
                padding: 8px 10px;
                color: $jet-black;
                cursor: pointer;
                margin: 2px;
                border-radius: 4px;

                &.is-active {
                  background: $green;
                  color: $white;
                  pointer-events: none;
                }
                &:hover {
                  background: $green;
                  color: $white;
                }

                .sort-by-result-content-popup {
                  .sort-by-result-content-text-popup {
                    margin-top: 3px;
                    margin-left: 3px;
                    display: flex;
                    margin-bottom: 3px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .shoppable-review-for-edit-product-button-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 18px;
    margin-bottom: 18px;
    margin-right: 2%;
    gap: 20px;
  }

.organizations-confirmation-modal {
  padding: 10px 20px;
  text-align: center;
  width: 430px;
  font-family: $font-family-averta;

  .organizations-confirmation-modal-content {
    .confirm-exit-top-section {
      display: flex;

      .confirm-exit-image {
        min-width: 80px;
      }
    }

    .confirmation-description {
      font-size: 18px;
      text-align: left;
      margin-bottom: 20px;
      margin-left: 20px;
      margin-top: 10px;
      font-weight: 700;
    }

    .organizations-confirmation-button-container {
      display: flex;
      align-items: center;
      justify-content: right;
      gap: 20px;
    }
  }
}

.modal-section-ingredient-name-list {
  overflow: hidden;
  font-family: $font-family-averta;

  .main-section-ingredient-name-list {
    height: auto;
    width: 103%;

    .ingredient-name-list {
      display: flex;
      justify-content: space-between;
      width: 660px;

      .Ingredient-text {
        font-weight: 700;
        font-size: 24px;
        color: $black;
        text-align: left;
        padding-left: 20px;
        display: flex;

        .ingredient-name-header {
          width: 236px;
        }

        .style-Ingredient-name-main {
          .style-Ingredient-name {
            color: $copper-rust;
            word-break: break-word !important;
            max-width: 350px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }

      .close-image-icon {
        margin-top: 4px;
        height: fit-content;
        width: fit-content;
        cursor: pointer;
      }
    }

    .count-Ingredient-name {
      text-align: left;
      margin-top: 6px;
      font-weight: 400;
      font-size: 16px;
      color: $shadow-gray;
      padding-left: 20px;
    }

    .Ingredient-name-list-main::-webkit-scrollbar {
      width: 10px;
    }

    .Ingredient-name-list-main::-webkit-scrollbar-track {
      background: $whispering-white-smoke;
      padding-top: 3px;
      padding-bottom: 5px;
      background-color: $snow;
    }

    .Ingredient-name-list-main::-webkit-scrollbar-thumb {
      background: $grainsboro;
      padding-top: 3px;
      padding-bottom: 5px;
      background-clip: content-box;
    }

    .Ingredient-name-list-main {
      display: grid;
      background-color: $white-smoke;
      height: auto;
      max-height: 245px;
      width: 100%;
      position: relative;
      left: -9px;
      grid-template-columns: 1fr 1fr 1fr;
      padding-left: 26px;
      overflow-x: hidden;
      overflow-y: visible;
      row-gap: 16px;
      padding-top: 23px;
      padding-bottom: 20px;
      padding-right: 19px;
      margin-top: 10px;
      font-size: 14px;
      color: $jet-black;
      font-weight: 700;

      .Ingredient-name-list {
        display: block;
        width: 190px;
        height: auto;
        padding: 10px 0px;
        border-radius: 8px;
        background-color: $white;
        border: 1px solid $grainsboro;
        padding-left: 15px;
        padding-right: 13px;
        align-items: center;
        text-align: left;
        white-space: pre-wrap;
      }
    }

    .main-section-keyword {
      display: flex;
      padding-left: 21px;
      position: relative;
      margin-top: 11px;
      margin-bottom: 4px;

      .keyword-section {
        font-weight: 700;
        font-size: 14px;
        color: $jet-black;
        width: 119px;
        display: flex;
        position: absolute;
      }

      .keyword-section-global {
        font-weight: 700;
        font-size: 14px;
        color: $jet-black;
        width: 119px;
        display: flex;
        position: absolute;
        top: 9px;
      }

      .keyword-right-section {
        display: flex;
        height: auto;
        flex-wrap: wrap;
        width: 519px;
        margin-left: 112px;

        .global-keyword-container {
          display: contents;
          float: left;

          .global-keyword-row {
            display: flex;
            justify-content: space-between;
            width: max-content;
            padding: 3px 5px;
            background-color: $grainsboro;
            margin: 5px 5px;
            border-radius: 4px;
            height: 25px;
            align-items: center;
            box-shadow: 0px 1px 0px 0px $grainsboro;
            position: relative;

            .global-keywords-name {
              max-width: 120px;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            }

            .cross-icon {
              margin-left: 6px;
              margin-bottom: 2px;
            }

            .cross-icon:hover {
              cursor: pointer;
            }
          }
        }
      }

      input[type="text"].placeholder-keywords::-webkit-input-placeholder {
        font-style: italic;
        color: $gunmetal-grey;
      }

      input[type="text"].placeholder-keywords-enter::-webkit-input-placeholder {
        font-style: italic;
        color: $gunmetal-grey;
      }

      .placeholder-keywords {
        box-shadow: none;
        border: none;
        color: $gunmetal-grey;
        font-size: 14px;
        font-weight: 400;
        padding: 0px 6px;
        width: calc(100% - 200px);
      }

      .placeholder-keywords-enter {
        box-shadow: none;
        border: none;
        color: $gunmetal-grey;
        font-size: 14px;
        font-weight: 400;
        padding: 0px 6px;
      }
    }

    .hr-main-container {
      padding-left: 21px;
      padding-right: 27px;

      .hr-line {
        width: 100%;
        height: 1px;
      }
    }

    .main-section-button {
      display: flex;
      width: 100%;
      gap: 20px;
      justify-content: flex-end;
      margin-top: 27px;
      padding-right: 42px;
    }
  }
}
