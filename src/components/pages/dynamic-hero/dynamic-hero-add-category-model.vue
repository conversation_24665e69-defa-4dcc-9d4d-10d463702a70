<template>
  <Modal @close="$emit('close')">
    <template #isAddCategoriesGroupModal>
      <div class="add-category-modal">
        <div class="title-section">
          <div class="title">{{ categoriesTitle }}</div>
          <div class="search-container-for-category">
            <div class="search-box">
              <input
                type="text"
                class="search-input-box"
                autocomplete="off"
                placeholder="Find a Category"
                @input="onQueryInput"
                v-model="localQueryCategory"
                @keypress.enter="handleGetCategorySearch"
              />
              <img
                alt=""
                :class="
                  queryCategory
                    ? 'search-icon-green-image'
                    : 'search-icon-green-image disabled-search-icon'
                "
                @click="handleGetCategorySearch"
                src="@/assets/images/search-grey.png"
              />
              <img
                alt=""
                class="exit-search-icon"
                v-if="isSearchExitEnable"
                @click="handleResetQuery"
                src="@/assets/images/exit-search.svg?skipsvgo=true"
              />
            </div>
          </div>
        </div>

        <div class="add-group-content" id="addGroupContent">
          <div v-if="isTableDataLoading" class="table-image-loader">
            <div class="loader"></div>
          </div>
          <div class="category-popup-container">
            <div class="container" v-if="!isTableDataLoading">
              <div
                v-if="!!categoryUpdateList && displayNoRecipeSection && isSearchExitEnable"
                class="no-recipe-result"
              >
                {{ $t("COMMON.NO_RESULTS") }}
              </div>
              <button
                type="button"
                class="card"
                v-for="(category, index) in formattedCategoryPopUp"
                :key="index"
                :class="{
                  'selected-categories': category.isChecked,
                  'already-added-category': category.isAlreadyAddedCategory
                }"
                @click="handleCategoryChecked(category)"
              >
                <div class="card-image">
                  <img
                    alt=""
                    class="image"
                    :src="category?.data?.[lang]?.image || defaultImage"
                  />
                </div>
                <div class="card-title">
                  {{ category?.data?.[lang]?.name || "" }}
                </div>
                <div class="total-recipe">
                  <span v-if="category.totalRecipes > 1">
                    {{ category.totalRecipes }} recipes
                  </span>
                  <span v-else>{{ category.totalRecipes }} recipe</span>
                </div>
                <span class="checkmark"></span>
              </button>
            </div>
            <div class="load-button" v-if="canLoadMore && !isTableDataLoading">
              <button type="button" class="load-more" @click="handleLoadMore">
                {{ $t("COMMON.LOAD_MORE") }}
              </button>
            </div>
          </div>
          <div class="add-group-content-space"></div>
        </div>

        <div class="done-section-hero">
          <button type="button" class="done-button-hero" @click="handleDone">
            {{ saveButtonMessage }}
          </button>
        </div>
      </div>
    </template>
  </Modal>
</template>

<script setup>
const props = defineProps({
  queryCategory: String,
  isSearchExitEnable: Boolean,
  isTableDataLoading: Boolean,
  formattedCategoryPopUp: Array,
  defaultImage: String,
  lang: String,
  saveButtonMessage: String,
  categoriesTitle: String,
  categoriesSubtitle: String,
  categoryUpdateList: Array,
  canLoadMore: Boolean,
  displayNoRecipeSection: Boolean,
});

const emits = defineEmits([
  "close",
  "getCategorySearch",
  "resetQuery",
  "isCategoryChecked",
  "loadMore",
  "done",
]);

const localQueryCategory = ref(props.queryCategory);

const onQueryInput = (event) => {
  emits("update:queryCategory", event.target.value);
};

const handleGetCategorySearch = () => emits("getCategorySearch");
const handleCategoryChecked = (category) => emits("isCategoryChecked", category);
const handleLoadMore = () => emits("loadMore");
const handleDone = () => emits("done");
const handleResetQuery = () => {
  emits("resetQuery");
  localQueryCategory.value = '';
}
</script>
