import defaultImage from "@/assets/images/default_recipe_image.png";
import frenchFlag from "@/assets/images/france-flag.png";
import spanishFlag from "@/assets/images/spain-flag.png";
import { useStore } from "vuex";
import { useDelayTimer } from "@/composables/useDelayTimer";

export function useCategoryFormMethods(state, stores) {
  const store = useStore();
  const {
    userDataStore,
    categoryStore,
    parseDurationString,
  } = stores;
  const { delay } = useDelayTimer();

  const handleSlugInput = async () => {
    if (state.props.isEdit && !state.isInitializing.value) {
      state.hasChanges.value = true;
    }
    state.isSlugValidating.value = true;
    await delay(2000);

    try {
      if (state.categoriesSlug.value?.trim()) {
        const response = await categoryStore.checkSlugAsync(
          state.categoriesSlug.value.trim(),
          state.lang.value
        );

        if (state.props.isEdit && state.categoryISIN.value) {
          state.hasSlugExist.value = response?.exists && response?.isin !== state.categoryISIN.value;
        } else {
          state.hasSlugExist.value = response?.exists || false;
        }
      } else {
        state.hasSlugExist.value = false;
      }
    } catch (error) {
      console.error("[IQ][CategoryForm] Error checking slug existence:", error);
      state.hasSlugExist.value = false;
    } finally {
      state.isSlugValidating.value = false;
    }
  };

  const getCategoryISINAsync = async () => {
    try {
      const response = await state.isinStore.getNewISINsAsync(state.lang.value, { entity: "recipeGroup" });
      state.newIsin.value = response?.isin || "";
    } catch (error) {
      console.error("[IQ][CategoryForm] Failed to fetch ISIN:", error);
    }
  };

  const getMediaImage = (media, currentLang) => {
    if (!media) return null;

    if (media.image) {
      return media.image;
    }

    if (media[currentLang]?.image) {
      return media[currentLang].image;
    }

    if (media[currentLang]?.thumbnailImageUrl) {
      return media[currentLang].thumbnailImageUrl;
    }

    if (media[currentLang]?.imageList?.length) {
      return media[currentLang].imageList[0].url;
    }

    return null;
  };

  const findImageInAvailableLanguages = (media) => {
    const availableLanguages = Object.keys(media);
    for (const lang of availableLanguages) {
      if (media[lang]?.image) {
        return media[lang].image;
      }
      if (media[lang]?.thumbnailImageUrl) {
        return media[lang].thumbnailImageUrl;
      }
      if (media[lang]?.imageList?.length) {
        return media[lang].imageList[0].url;
      }
    }
    return null;
  };

  const getRecipeImage = (recipe) => {
    if (!recipe) return defaultImage;
    
    if (recipe.image) {
      return recipe.image;
    }

    if (recipe.media) {
      const currentLang = userDataStore.getDefaultLang;
      const mediaImage = getMediaImage(recipe.media, currentLang);
      if (mediaImage) {
        return mediaImage;
      }

      const availableLangImage = findImageInAvailableLanguages(recipe.media);
      if (availableLangImage) {
        return availableLangImage;
      }
    }

    return defaultImage;
  };

  const getCombinedTime = (cook, prep) => {
    const cookTime = parseDurationString(cook);
    const prepTime = parseDurationString(prep);
    if (cookTime && prepTime) {
      return `${prepTime} prep + ${cookTime} cook`;
    }
    return null;
  };

  const getSingleTime = (time) => {
    if (!time) return null;
    return parseDurationString(time);
  };

  const getRecipeTime = (recipe) => {
    if (!recipe) return "none";

    if (recipe.totalTime && typeof recipe.totalTime === "string") {
      return recipe.totalTime;
    }

    if (recipe.time?.total) {
      return parseDurationString(recipe.time.total);
    }

    if (!recipe.time) return "none";

    const { cook, prep } = recipe.time;
    const combinedTime = getCombinedTime(cook, prep);
    if (combinedTime) return combinedTime;

    const singleTime = getSingleTime(cook) || getSingleTime(prep);
    if (singleTime) return singleTime;

    return "none";
  };

  const getRecipeTitle = (recipe) => {
    if (!recipe) return "";
    if (state.recipeVariantList.value.length) {
      getCategoryAssociations();
    }

    const currentLang = store.getters["userData/getDefaultLang"];

    if (recipe.title && typeof recipe.title === "string") {
      return recipe.title;
    }

    if (recipe.title[currentLang]) {
      return recipe.title[currentLang];
    }

    const availableLanguages = Object.keys(recipe.title);
    for (const lang of availableLanguages) {
      if (recipe.title[lang]) {
        return recipe.title[lang];
      }
    }

    return "";
  };

  const getIngredientCount = (recipe) => {
    if (!recipe) return 0;

    const currentLang = store.getters["userData/getDefaultLang"];

    if (recipe.ingredients && typeof recipe.ingredients === "object") {
      if (
        recipe.ingredients[currentLang] &&
        Array.isArray(recipe.ingredients[currentLang])
      ) {
        return recipe.ingredients[currentLang].length;
      }

      if (Array.isArray(recipe.ingredients)) {
        return recipe.ingredients.length;
      }

      const availableLanguages = Object.keys(recipe.ingredients);
      for (const lang of availableLanguages) {
        if (Array.isArray(recipe.ingredients[lang])) {
          return recipe.ingredients[lang].length;
        }
      }
    }

    return 0;
  };

  const getPromotedRecipeActions = (recipe) => {
    return [
      {
        isDisable: false,
        isInactive: false,
        key: [state.RECIPE_ACTION_CASE.PREVIEW, recipe.isin],
        label: "Preview",
      },
      {
        isDisable: false,
        isInactive: false,
        key: [state.RECIPE_ACTION_CASE.UNPROMOTE, recipe.isin],
        label: "Unpromote",
      },
    ];
  };

  const getCategoryRecipeActions = (recipe) => {
    return [
      {
        isDisable: false,
        isInactive: false,
        key: [state.RECIPE_ACTION_CASE.PREVIEW, recipe.isin],
        label: "Preview",
      },
      {
        isDisable: false,
        isInactive: false,
        key: [state.RECIPE_ACTION_CASE.REMOVE, recipe.isin],
        label: "Remove",
      },
    ];
  };

  const displayLanguageCode = (item) => {
    if (item) {
      const arr = item.split("-");
      return arr[0].toUpperCase();
    }
    return "";
  };

  const isDeleteVariantVisible = (categoryVariant) => {
    return state.categoryAssociations.value[categoryVariant.lang];
  };

  const getLanguageName = (langCode) => {
    const lang = langCode.split("-")[0];

    const langMap = {
      fr: "French",
      es: "Spanish",
    };

    return langMap[lang] || langCode;
  };

  const getLanguageFlag = (langCode) => {
    const lang = langCode.split("-")[0];

    const flagMap = {
      fr: frenchFlag,
      es: spanishFlag,
    };

    return flagMap[lang] || "";
  };

  const inputContentChanged = () => {
    if (state.props.isEdit && !state.isInitializing.value) {
      state.hasChanges.value = true;
      if (state.recipeVariantList.value.length) {
        getCategoryAssociations();
      }
    }
  };

  const checkOperationStatusAsync = async (operationId) => {
    await getOperationStatusAsync(operationId);
    if (state.isAbortedCheckingOperationStatus.value) {
      throw new Error("Operation was aborted by user");
    }
  };

  const getOperationStatusAsync = async (operationId) => {
    await categoryStore.getOperationStatusAsync(operationId);
  };

  const getCategoryAssociations = async () => {
    if (!state.props.isEdit || !state.categoryISIN.value) return;

    const promises = [];
    const variantList = [];

    state.recipeVariantList.value.forEach((langVariant) => {
      if (langVariant.lang !== state.lang.value) {
        promises.push(
          categoryStore.getCategoryAssociationsAsync(
            state.categoryISIN.value,
            langVariant.lang,
            0,
            15
          )
            .then(() => {
              const response = categoryStore.categoryAssociations;
              const object = {
                [langVariant.lang]:
                  response?.recipes?.length ||
                  response?.recipeGroups?.length ||
                  0,
              };
              variantList.push(object);
            })
        );
      }
    });

    await Promise.all(promises);
    state.categoryAssociations.value = Object.assign({}, ...variantList);
  };
  const cleanup = () => {
    state.isSlugValidating.value = false;
  };

  return {
    handleSlugInput,
    getCategoryISINAsync,
    getRecipeImage,
    getRecipeTime,
    getRecipeTitle,
    getIngredientCount,
    getPromotedRecipeActions,
    getCategoryRecipeActions,
    displayLanguageCode,
    isDeleteVariantVisible,
    getLanguageName,
    getLanguageFlag,
    inputContentChanged,
    checkOperationStatusAsync,
    getOperationStatusAsync,
    getCategoryAssociations,
    cleanup,
  };
}
