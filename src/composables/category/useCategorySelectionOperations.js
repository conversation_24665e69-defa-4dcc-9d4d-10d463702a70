import { nextTick } from "vue";

export function useCategorySelectionOperations(state, recipeOperations) {

  const selectRecipes = () => {
    state.isSelectionEnabled.value = true;
    state.checkSelectedRecipes.value = 0;
    state.recipeDataForCategories.value.forEach((recipe) => {
      if (recipe.isSelectedToDelete === undefined) {
        recipe.isSelectedToDelete = false;
      }
    });
    if (state.selectedProductsAcrossPages.value.length) {
      recipeOperations.restorePageSelections();
    }
  };

  const selectAllMatches = () => {
    const isSelecting = !state.selectionOfRecipes.value[0].isSelected;
    state.selectionOfRecipes.value[0].isSelected = isSelecting;
    state.recipeDataForCategories.value = state.recipeDataForCategories.value.map((item) => ({
      ...item,
      isSelectedToDelete: isSelecting,
    }));

    const currentPageIsins = state.recipeDataForCategories.value.map(item => item.isin);

    if (isSelecting) {
      state.selectedProducts.value = state.recipeDataForCategories.value.filter(item => item.isSelectedToDelete);
    } else {
      state.selectedProducts.value = [];
      state.selectedProductsAcrossPages.value = state.selectedProductsAcrossPages.value.filter(
        item => !currentPageIsins.includes(item.isin)
      );
    }

    state.updateSelectionCount();
  };

  const tableRowClickAction = (data, index) => {
    if (state.isSelectionEnabled.value) {
      handleCheckboxClick(index, data, null);
    }
  };

  const updateSelectedProducts = (item, wasSelected) => {
    if (!wasSelected) {
      const exists = state.selectedProducts.value.find((p) => p.isin === item.isin);
      if (!exists) {
        state.selectedProducts.value.push(item);
      }
    } else {
      state.selectedProducts.value = state.selectedProducts.value.filter(
        (insideItem) => insideItem.isin !== item.isin
      );
      state.selectedProductsAcrossPages.value = state.selectedProductsAcrossPages.value.filter(
        (insideItem) => insideItem.isin !== item.isin
      );
    }
  };

  const handleCheckboxClick = async (index, data, event) => {
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    if (!state.isSelectionEnabled.value || !data) {
      return;
    }

    let actualIndex = index;
    if (actualIndex === undefined || actualIndex === null) {
      actualIndex = state.recipeDataForCategories.value.findIndex(
        (item) => item.isin === data.isin
      );
    }

    if (actualIndex < 0 || actualIndex >= state.recipeDataForCategories.value.length) {
      console.error(
        "[IQ][CategoryForm] Could not find item with ISIN:",
        data.isin,
        "in recipeDataForCategories"
      );
      return;
    }

    const item = state.recipeDataForCategories.value[actualIndex];
    const wasSelected = item.isSelectedToDelete || false;
    item.isSelectedToDelete = !wasSelected;
    state.recipeDataForCategories.value = [...state.recipeDataForCategories.value];

    updateSelectedProducts(item, wasSelected);

    await nextTick();
    state.updateSelectionCount();
    await nextTick();
    recipeOperations.checkSelected();
  };

  const cancelSelect = () => {
    state.isSelectionEnabled.value = false;
    state.selectedProducts.value = [];
    state.selectedProductsAcrossPages.value = [];
    state.selectionOfRecipes.value[0].isSelected = false;

    if (state.recipeDataForCategories.value.length) {
      const updatedRecipes = state.recipeDataForCategories.value.map((item) => ({
        ...item,
        isSelectedToDelete: false,
      }));
      state.recipeDataForCategories.value = updatedRecipes;
    }

    state.updateSelectionCount();
  };

  const removeAllSelected = () => {
    const updatedRecipes = state.recipeDataForCategories.value.map((item) => ({
      ...item,
      isSelectedToDelete: false,
    }));
    state.recipeDataForCategories.value = updatedRecipes;

    state.selectionOfRecipes.value[0].isSelected = false;
    state.selectedProducts.value = [];
    state.selectedProductsAcrossPages.value = [];

    state.updateSelectionCount();
  };

  const deleteSelectProductMatches = async () => {
    try {
      const selectedIsinsToRemove = [];

      state.recipeDataForCategories.value.forEach((recipe) => {
        if (recipe.isSelectedToDelete) {
          selectedIsinsToRemove.push(recipe.isin);
          state.removeRecipeList.value.push(recipe);
        }
      });
      state.selectedProductsAcrossPages.value.forEach((recipe) => {
        if (!selectedIsinsToRemove.includes(recipe.isin)) {
          selectedIsinsToRemove.push(recipe.isin);
          state.removeRecipeList.value.push(recipe);
        }
      });

      if (selectedIsinsToRemove.length === 0) {
        console.warn("[IQ][CategoryForm] No recipes selected for removal");
        return;
      }

      state.recipesAfterPageChange.value = state.recipesAfterPageChange.value.filter(
        (data) => {
          return !selectedIsinsToRemove.includes(data.isin);
        }
      );

      state.recipeDataForCategories.value = state.recipeDataForCategories.value.filter(
        (data) => {
          return !selectedIsinsToRemove.includes(data.isin);
        }
      );

      state.recipeMatchesIsinsRemove.value.push(...selectedIsinsToRemove);

      state.selectedCategoryRecipe.value = state.selectedCategoryRecipe.value.filter(
        (isin) => !selectedIsinsToRemove.includes(isin)
      );

      state.addedIsins.value = state.addedIsins.value.filter(
        (isin) => !selectedIsinsToRemove.includes(isin)
      );

      state.hasChanges.value = true;

      const isBulkDeletion =
        selectedIsinsToRemove.length > 1 ||
        state.selectedProductsAcrossPages.value.length > 0;
      state.selectedProducts.value = [];
      state.selectedProductsAcrossPages.value = [];
      state.selectionOfRecipes.value[0].isSelected = false;
      state.isSelectionEnabled.value = false;

      if (state.props.isEdit && state.categoryISIN.value) {

        if (isBulkDeletion) {
          state.fromRecipe.value = 0;
        }

        await recipeOperations.getRecipeDataForCategoriesAsync(state.categoryISIN.value);
        await recipeOperations.handlePaginationRedirection();
      } else {
        state.recipeForCategoriesTotal.value = state.recipeDataForCategories.value.length;
      }
    } catch (error) {
      console.error("[IQ][CategoryForm] Error removing selected recipes:", error);
    }
  };

  return {
    selectRecipes,
    selectAllMatches,
    tableRowClickAction,
    updateSelectedProducts,
    handleCheckboxClick,
    cancelSelect,
    removeAllSelected,
    deleteSelectProductMatches,
  };
}
