import { useCategoryStore } from "@/stores/category.js";
import { useEditSearchStore } from "@/stores/editSearch.js";
import frenchFlag from "@/assets/images/france-flag.png";
import spanishFlag from "@/assets/images/spain-flag.png";
import { useStore } from "vuex";
import { useNuxtApp } from "#app";

export function useCategoryDataOperations(state) {
  const categoryStore = useCategoryStore();
  const editSearchStore = useEditSearchStore();
  const store = useStore();
  const { $t } = useNuxtApp();

  const CATEGORY_STATUS = {
    PUBLISHED: "published",
  };

  const getSearchConfigAsync = async (isin) => {
    try {
      const response = await editSearchStore.getEditSearchAsync();
      if (response?.filters?.length) {
        const searchCategoryISINList = response.filters.find(data => data.type === $t('SEARCH_FILTER.CATEGORIES'));
        if (searchCategoryISINList?.values?.length) {
          state.searchConfig.value = searchCategoryISINList.values.some(value => value.isin === isin);
        }
      }
    } catch (error) {
      console.error("[IQ][CategoryForm] Error loading search config:", error);
      state.searchConfig.value = false;
    }
  };

  const getEditCategoryListAsync = async (isin, lang) => {
    try {
      await updateCategoryDetails(isin, lang);
    } catch (error) {
      console.error(
        "[IQ][CategoryForm] Error in getEditCategoryListAsync:",
        error
      );
    }
  };

  const processCategoryGroupData = (response) => {
    const { localized } = response;
  
    if (!localized) return;
  
    state.selectedDefaultLang.value.forEach((language) => {
      if (localized?.[language] && language !== state.lang.value) {
        const newVariantData = {
          name: localized[language].name,
          lang: language,
        };
  
        state.recipeVariantList.value.push(newVariantData);
        state.initiallyVariantSupported.value.push(newVariantData);
      }
    });
  };

  const updateCategoryDetails = async (isin, lang) => {
    const variant = !!(state.finalAvailableLangs.value && state.finalAvailableLangs.value.length > 1);
    await categoryStore.getCategoryDataAsync(isin, lang, variant);
    const categoryData = categoryStore.getCategoryData;
    processCategoryGroupData(categoryData);
    state.categoryISIN.value = categoryData.isin || "";
    state.categoriesState.value = categoryData.state || "";
    state.categoriesSlug.value = categoryData.slug?.[lang] || "";

    state.isPublish.value = categoryData.state === CATEGORY_STATUS.PUBLISHED;
    state.isCategoryAlertIcon.value = categoryData?.hasAlert ?? false;
    if (categoryData) {
      if (categoryData.name && !state.categoriesName.value) {
        state.categoriesName.value = categoryData.name;
      }
      if (categoryData.slug && !state.categoriesSlug.value) {
        state.categoriesSlug.value = categoryData.slug;
      }
      if (categoryData.image && !state.image.value) {
        state.image.value = categoryData.image;
      }
    }
  };

  const getLanguageName = (langCode) => {
    const lang = langCode.split("-")[0];

    const langMap = {
      fr: "French",
      es: "Spanish",
    };

    return langMap[lang] || langCode;
  };

  const getLanguageFlag = (langCode) => {
    const lang = langCode.split("-")[0];

    const flagMap = {
      fr: frenchFlag,
      es: spanishFlag,
    };

    return flagMap[lang] || "";
  };

  const openRecipeVariantPopUp = () => {
    state.hasRecipeVariantLanguagePopUp.value = true;
    state.hasRecipeVariantLanguageResult.value = false;
    state.hasDisableSelectLanguageButton.value = false;
    state.recipeVariantLanguage.value = "";
  };

  const setRecipeVariantLanguageMatches = (value, index) => {
    state.recipeVariantLanguage.value = value.language;
    state.recipeVariantLanguageIndex.value = index;
    state.hasRecipeVariantLanguageResult.value = false;
    state.hasDisableSelectLanguageButton.value = true;

    state.recipeVariantLanguageList.value.forEach((data, idx) => {
      if (data.language === state.recipeVariantLanguage.value) {
        state.recipeVariantLanguageList.value.splice(idx, 1);
        state.recipeVariantLanguageList.value.unshift(data);
      }
    });
  };

  const showRecipeVariantLanguageMatches = () => {
    state.hasRecipeVariantLanguageResult.value = !state.hasRecipeVariantLanguageResult.value;
  };

  const nextCategoryVariantNameModalPopUp = (item) => {
    if (item === "") {
      state.recipeVariantSelectedLanguage.value =
        state.recipeVariantLanguageList.value[0]?.language || "";
      state.recipeVariantLanguage.value =
        state.recipeVariantLanguageList.value[0]?.language || "";
    } else {
      state.recipeVariantSelectedLanguage.value = item;
    }
    state.hasRecipeVariantLanguagePopUp.value = false;
    state.isAddVariantCategoryNamePopUp.value = true;
    state.hasRecipeVariantLanguageResult.value = false;
  };

  const backToSelectLanguageVariantPopUp = () => {
    state.isAddVariantCategoryNamePopUp.value = false;
    state.hasRecipeVariantLanguagePopUp.value = true;
  };

  const addRecipeVariant = (item) => {
    state.variantName.value = item;
    if (state.variantName.value !== "") {
      const newVariantData = {
        name: item.trim(),
        lang: state.recipeVariantLanguage.value,
      };
      state.recipeVariantList.value.push(newVariantData);
      state.hasChanges.value = true;
      state.isAddVariantCategoryNamePopUp.value = false;
      state.variantName.value = "";

      state.recipeVariantLanguageList.value = state.recipeVariantLanguageList.value.filter(
        (data) => data.language !== state.recipeVariantLanguage.value
      );

      state.saveRemovedCategoryVariants.value =
        state.saveRemovedCategoryVariants.value.filter(
          (data) => data !== state.recipeVariantLanguage.value
        );
    }
  };

  const deleteCategoryVariant = (categoryVariant, index) => {
    state.categoryVariantDataIndex.value = index;
    
    if (
      state.initiallyVariantSupported.value.some(
        (variant) => variant.lang === categoryVariant.lang
      )
    ) {
      state.saveRemovedCategoryVariants.value.push(categoryVariant.lang);
    }

    const langData = {
      language: categoryVariant.lang,
      language_name: getLanguageName(categoryVariant.lang),
      languageFlag: getLanguageFlag(categoryVariant.lang),
    };

    if (
      !state.recipeVariantLanguageList.value.some(
        (lang) => lang.language === categoryVariant.lang
      )
    ) {
      state.recipeVariantLanguageList.value.push(langData);
    }
  };

  const deleteCategoryVariantAsync = () => {
    state.recipeVariantList.value.splice(state.categoryVariantDataIndex.value, 1);
    state.hasChanges.value = true;
  };

  const processAvailableLanguages = (availableLangs, currentLang) => {
    return availableLangs
      ?.filter((lang) => lang !== currentLang)
      .map((lang) => ({
        language: lang,
        language_name: getLanguageName(lang),
        languageFlag: getLanguageFlag(lang),
      }));
  };

  const handleProjectReady = (isProjectReady, resolve) => {
    if (isProjectReady) {
      const availableLangs = store.getters["userData/getAvailableLangs"];
      state.finalAvailableLangs.value = availableLangs;
      state.selectedDefaultLang.value = availableLangs;

      const currentLang = state.lang.value;
      state.recipeVariantLanguageList.value = processAvailableLanguages(availableLangs, currentLang);
    }
    resolve();
  };

  const initializeLanguageVariants = async () => {
    try {
      return new Promise((resolve) => {
        state.readyProject(({ isProjectReady }) => {
          handleProjectReady(isProjectReady, resolve);
        });
      });
    } catch (error) {
      console.error("[IQ][CategoryForm] Error initializing language variants:", error);
    }
  };

  const checkSlugExistence = async () => {
    if (!state.categoriesSlug.value?.trim()) {
      state.hasSlugExist.value = false;
      return;
    }

    try {
      const response = await categoryStore.checkSlugExistenceAsync(
        state.categoriesSlug.value.trim(),
        state.lang.value
      );
      
      if (state.props.isEdit && state.categoryISIN.value) {
        state.hasSlugExist.value = response?.exists && response?.isin !== state.categoryISIN.value;
      } else {
        state.hasSlugExist.value = response?.exists || false;
      }
    } catch (error) {
      console.error("[IQ][CategoryForm] Error checking slug existence:", error);
      state.hasSlugExist.value = false;
    }
  };

  return {
    getSearchConfigAsync,
    getEditCategoryListAsync,
    processCategoryGroupData,
    updateCategoryDetails,
    getLanguageName,
    getLanguageFlag,
    openRecipeVariantPopUp,
    setRecipeVariantLanguageMatches,
    showRecipeVariantLanguageMatches,
    nextCategoryVariantNameModalPopUp,
    backToSelectLanguageVariantPopUp,
    addRecipeVariant,
    deleteCategoryVariant,
    deleteCategoryVariantAsync,
    processAvailableLanguages,
    handleProjectReady,
    initializeLanguageVariants,
    checkSlugExistence,
  };
}
