<template>
  <client-only>
    <content-wrapper :is-body-loading="isLoading" body-classes="video-inner-section">
      <template v-slot:title>
          <span>{{ $t('ROCHE.VIDEOS') }}</span>
        </template>

        <template v-slot:head>
          <div class="header-button">
            <button
              type="button"
              @click="uploadVideo($event)"
              v-if="!hasReloadTextCheck"
              class="btn-green padding-zero"
            >
              <label for="file" class="roche-video-upload-label">{{
                $t("ROCHE.NEW_VIDEO")
              }}</label>
              <input
                id="file"
                type="file"
                class="visibility-hidden display-none"
                title="NEW VIDEO"
                @change="checkUploadedVideoFileAsync"
                accept=".mp4"
              />
            </button>
            <div
              v-if="!hasReloadTextCheck"
              class="text-h3 color-grey font-italic font-normal"
            >
              {{ $t("ROCHE.MAX_SIZE_FORMAT") }}
            </div>
          </div>
        </template>
        <div class="roche-video-main-table">
          <simple-table
            v-if="videoDataList.length"
            :column-names="columnNames"
            :column-keys="columnKeys"
            :data-source="videoDataList"
            table-class="roche-video"
          >
            <template v-slot:videoName="props">
              <div class="roche-video-container">
                <div class="video-name-text font-bold text-h3">
                  {{ props?.data?.name ?? "" }}
                </div>
                <div class="roche-video-publisher-form">
                  <button
                    type="button"
                    @click="playVideo(props.data.link)"
                    class="btn-reset simple-data-tooltip"
                    :data-tooltip-text="$t('VIDEO.CLICK_TO_PLAY_VIDEO')"
                  >
                    <img
                      src="~/assets/images/circle-play.svg?skipsvgo=true"
                      alt="Play Video"
                    />
                  </button>
                </div>
              </div>
            </template>

            <template v-slot:cloudfrontLink="props">
              <div class="roche-video-cloudfront-row">
                <div class="color-navy-blue text-h4 cursor-pointer font-normal">
                  <span
                    class="text"
                    :id="`cloudfrontID${videoDataList.indexOf(props.data)}`"
                    @click="openVideoTab(props.data)"
                    >{{
                      props?.data?.link ?? ""
                    }}</span
                  >
                </div>
                <div class="roche-video-copy-clipboard">
                  <button
                    type="button"
                    :data-tooltip-text="$t('VIDEO.COPY_LINK_TO_CLIPBOARD')"
                    class="btn-reset simple-data-tooltip"
                    @click="copyVideoCloudfront(videoDataList.indexOf(props.data))"
                  >
                    <img src="~/assets/images/copy-icon.png" alt="copy" />
                  </button>
                </div>
              </div>
            </template>

            <template v-slot:size="props">
              <span class="text-light-h3 color-gunmetal-grey">
                {{ bytesToMegabytes(props.data?.size) ?? "" }}
              </span>
            </template>

            <template v-slot:added="props">
              <span class="text-light-h3 color-gunmetal-grey">
                {{ convertTimestampToCustomFormat(props.data?.createDate) ?? "" }}
              </span>
            </template>
            <template v-slot:actions="props">
              <div class="roche-video-actions">
                <simple-actions
                  :is-edit-btn-displayed="false"
                  @deleteOnClick="deleteVideo(props.data.name)"
                ></simple-actions>
              </div>
            </template>
          </simple-table>
        </div>
        <noResultFound
          v-if="!videoDataList.length"
          :description="
            hasReloadTextCheck
              ? $t('COMMON.RELOAD_PAGE')
              : $t('NO_RESULT.VIDEO')
          "
        />
    </content-wrapper>
  </client-only>
</template>
<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import { useStore } from "vuex";
import { useVideoStore } from "~/stores/video.js";
import recipeVideo from "@/components/recipeVideo";
import ProcessModal from "../components/modals/process-modal.vue";
import ConfirmModal from "../components/modals/confirm-modal.vue";
import { CONFIRM_MODAL_TYPE } from "../models/confirm-modal.model.js";
import noResultFound from "@/components/no-result-found";
import ContentWrapper from "@/components/content-wrapper/content-wrapper";
import SimpleTable from "@/components/simple-table/simple-table";
import { useTimeUtils } from "~/composables/useTimeUtils";
import { useCommonUtils } from "~/composables/useCommonUtils";
import { useRefUtils } from "~/composables/useRefUtils";
import { useNuxtApp } from "#app";
import { useProjectLang } from "../composables/useProjectLang.js";
import { useBaseModal } from "~/composables/useBaseModal.js";
import { PROCESS_MODAL_TYPE } from "../models/process-modal.model.js";

const { convertTimestampToCustomFormat } = useTimeUtils();
const { $eventBus, $keys, $t } = useNuxtApp();

const store = useStore();

// Pinia store
const {
  getVideoDataAsync,
  uploadVideoAsync,
  deleteVideoAsync,
  isVideoNameExists,
  isLoading,
  hasReloadTextCheck,
  videoDataList,
} = useVideoStore();

const { getRef } = useRefUtils();
const { triggerLoading } = useCommonUtils();
const { readyProject } = useProjectLang();

// Modal configuration
const { openModal, closeModal: closeBaseModal } = useBaseModal({
  recipeVideo: recipeVideo,
  "videoPageConfirmModal": ConfirmModal,
  "videoProcessModal": {
    component: ProcessModal,
    skipClickOutside: true,
    skipEscapeClick: true,
  },
});

const videoFile = ref([]);
const videoFilesName = ref([]);
const lang = ref("");

// Table configuration
const columnNames = ref(["Video Name", "Cloudfront Link", "Size", "Added", ""]);
const columnKeys = ref([
  "videoName",
  "cloudfrontLink",
  "size",
  "added",
  "actions",
]);

onMounted(async () => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      document.addEventListener("keyup", handleESCClickOutside);
      lang.value = store.getters["userData/getDefaultLang"];
      await getVideoDataListAsync();
    }
  });
});

onBeforeUnmount(() => {
  document.removeEventListener("keyup", handleESCClickOutside);
});

const handleESCClickOutside = (event) => {
  if (event?.key === "Escape") {
    closeBaseModal("recipeVideo");
    closeBaseModal("videoPageConfirmModal");
  }
};

const playVideo = (data) => {
  openModal({
    name: "recipeVideo",
    props: {
      videoLink: data,
      closeModal: () => closeBaseModal("recipeVideo"),
    },
  });
};

const openVideoTab = (data) => {
  window.open(data.link, "_blank");
};

const bytesToMegabytes = (bytes) => {
  const megabytes = bytes / (1024 * 1024);
  return `${megabytes.toFixed(2)} MB`;
};

const getVideoDataListAsync = async () => {
  try {
    await getVideoDataAsync();
  } catch (e) {
    console.error(`${$keys.KEY_NAMES.ERROR_IN} getVideoDataAsync`, e);
  }
};

const copyVideoCloudfront = (index) => {
  const copyElement = getRef(`cloudfrontID${index}`);
  navigator.clipboard.writeText(copyElement.innerHTML);
  $eventBus.emit("copyToClipboard");
};

const deleteVideo = (name) => {
  openModal({
    name: "videoPageConfirmModal",
    props: {
      modalType: CONFIRM_MODAL_TYPE.DELETE,
      title: $t('DESCRIPTION_POPUP.DELETE_VIDEO'),
      description: `${$t('DESCRIPTION_POPUP.DELETE_POPUP')} video?`,
    },
    onClose: (response) => response && deleteRocheVideoAsync(name),
  });
};

const deleteRocheVideoAsync = async (name) => {
  try {
    openModal({
      name: "videoProcessModal",
      props: { modalType: PROCESS_MODAL_TYPE.DELETING }
    });
    await deleteVideoAsync(name);
    triggerLoading($keys.KEY_NAMES.NEW_DELETED_SUCCESS);
    await getVideoDataListAsync();
  } catch (e) {
    console.error(`${$keys.KEY_NAMES.ERROR_IN} deleteRocheVideoAsync:`, e);
  } finally {
    closeBaseModal("videoProcessModal");
  }
};

const resetFileInput = () => {
  const fileInput = document.getElementById("file");
  if (fileInput) {
    fileInput.value = ""; // Reset to empty string
  }
};

const checkUploadedVideoFileAsync = async (event) => {
  const files = event.target.files;
  videoFile.value = files.length ? files : null;
  const reg = /\.mp4$/i;

  if (videoFile?.value?.length) {
    videoFilesName.value = videoFile.value[0].name
      .toLowerCase()
      .replaceAll(" ", "_");

    const fileSize = videoFile.value[0].size;
    if (fileSize > 250 * 1024 * 1024) {
      resetFileInput();
      openModal({
        name: "videoPageConfirmModal",
        props: {
          title: $t('VIDEO.VIDEO_SIZE_LIMIT_EXCEEDED'),
          descriptionRed: $t('VIDEO.MAX_SIZE_FOR_VIDEO'),
          hideCancelBtn: true,
          modalType: CONFIRM_MODAL_TYPE.UNABLE,
        },
        onClose: () => closeBaseModal("videoPageConfirmModal"),
      });
    } else if (!reg.test(videoFilesName.value)) {
      resetFileInput();
      openModal({
        name: "videoPageConfirmModal",
        props: {
          title: $t('COMMON.INVALID_VIDEO_FILE'),
          descriptionRed: $t('COMMON.ACCEPTED_FILE_FORMATS'),
          hideCancelBtn: true,
          modalType: CONFIRM_MODAL_TYPE.UNABLE,
        },
        onClose: () => closeBaseModal("videoPageConfirmModal"),
      });
    }
    else if (isVideoNameExists(videoFilesName.value)) {
      $eventBus.emit("videoNameExist");
    } else {
      openModal({
        name: "videoProcessModal",
        props: {
          modalType: PROCESS_MODAL_TYPE.UPLOADING,
        },
      });
      const payload = new FormData();
      payload.append("file", videoFile.value[0]);
      payload.append("name", videoFilesName.value);

      try {
        await uploadVideoAsync(payload);
        triggerLoading($keys.KEY_NAMES.VIDEO_UPLOADED);
        await getVideoDataListAsync();
        uploadVideo(event);
      } catch (error) {
        console.error(
          `${$keys.KEY_NAMES.ERROR_IN} checkUploadedVideoFileAsync`,
          error
        );
        triggerLoading($keys.KEY_NAMES.VIDEO_UNEXPECTED_ERROR);
      } finally {
        closeBaseModal("videoProcessModal");
      }
    }
  }
};

const uploadVideo = (event) => {
  event.target.value = "";
};
</script>
