<template>
  <client-only>
      <content-wrapper class="padding-zero">
        <div class="background-image-categories-group" v-show="!isPageLoading">
          <img alt="" class="background-image" :src="`${image}`" />
          <div class="back-btn" @click="backToCategories()">
            <img
              alt=""
              class="back-arrow-image"
              src="@/assets/images/back-arrow.png"
            />
            <span class="back-to-categories text-title-2">{{
              $t('CATEGORY_GROUP.BACK_MESSAGE')
            }}</span>
          </div>
          <div class="head-btn">
            <button type="button"
              :class="
                isCampaignModified &&
                categoriesName.trim() != '' &&
                !isRecipeVariantNameEmpty &&
                (uploadImagePercentage == 0 || uploadImagePercentage == 100)
                  ? 'btn-green'
                  : 'btn-green disabled-button'
              "
              @click="displayPopup()"
              @keydown="preventEnterAndSpaceKeyPress($event)"
            >
              {{ categoriesStatus === "active" ? "Publish" : "Save" }}
            </button>
            <button type="button"
              @click="backToCategories()"
              @keydown="preventEnterAndSpaceKeyPress($event)"
              class="btn-green-outline"
            >
              {{ $t('BUTTONS.CANCEL_BUTTON') }}
            </button>
          </div>
        </div>
        <div class="input-categories-group-section" v-show="!isPageLoading">
          <div class="input-section">
            <div class="input-sub-section">
              <div class="left-section">
                <div
                  :style="{ backgroundImage: image ? `url('${image}')` : '' }"
                  class="image-section"
                  id="categoryImage"
                >
                  <div class="image-main-div" id="recipeVideo">
                    <div class="image-inner-container">
                      <div
                        class="progress-image"
                        v-show="
                          uploadImagePercentage >= 1 &&
                          uploadImagePercentage <= 99
                        "
                      >
                        <div class="progress-image-content">
                          <div
                            v-show="
                              uploadImagePercentage >= 1 &&
                              uploadImagePercentage <= 5
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-0.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 6 &&
                              uploadImagePercentage <= 11
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-6.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 12 &&
                              uploadImagePercentage <= 17
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-12.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 18 &&
                              uploadImagePercentage <= 24
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-18.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 25 &&
                              uploadImagePercentage <= 30
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-25.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 31 &&
                              uploadImagePercentage <= 36
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-31.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 37 &&
                              uploadImagePercentage <= 41
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-37.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 42 &&
                              uploadImagePercentage <= 49
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-42.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 50 &&
                              uploadImagePercentage <= 55
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-50.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 56 &&
                              uploadImagePercentage <= 61
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-56.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 62 &&
                              uploadImagePercentage <= 67
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-62.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 68 &&
                              uploadImagePercentage <= 74
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-68.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 75 &&
                              uploadImagePercentage <= 80
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-75.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 81 &&
                              uploadImagePercentage <= 86
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-81.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 87 &&
                              uploadImagePercentage <= 92
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-87.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 93 &&
                              uploadImagePercentage <= 98
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-93.svg?skipsvgo=true"
                            />
                          </div>
                          <div v-show="uploadImagePercentage == 99">
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-uploaded.svg?skipsvgo=true"
                            />
                          </div>
                          <div class="upload-text">
                            <div
                              class="upload-heading text-light-h4"
                              v-if="
                                uploadImagePercentage >= 1 &&
                                uploadImagePercentage <= 98
                              "
                            >
                              Upload is in progress
                            </div>
                            <div class="upload-heading text-light-h4" v-else>Uploaded</div>
                            <span class="upload-media text-light-h6"
                              >{{ (loadedImageSize / 1024000).toFixed(1) }} of
                              {{ (uploadImageSize / 1024000).toFixed(1) }}
                              MB</span
                            >
                          </div>
                        </div>
                      </div>
                    </div>
                    <img
                      @click="DeleteImage()"
                      alt=""
                      class="delete-icon-image"
                      src="@/assets/images/deleteVideoBtn.png"
                      v-if="
                        image &&
                        (uploadImagePercentage == 0 ||
                          uploadImagePercentage == 100)
                      "
                    />
                    <img
                      v-if="
                        image &&
                        (uploadImagePercentage == 0 ||
                          uploadImagePercentage == 100)
                      "
                      class="display-image-section"
                      :src="`${image}`"
                      alt=""
                    />
                    <div
                      class="replace-image-tag"
                      v-if="
                        uploadImagePercentage == 0 ||
                        uploadImagePercentage == 100
                      "
                    >
                      <div class="hover-image">
                        <input
                          type="file"
                          class="upload-input"
                          title="Update Picture"
                          @click="uploadSameImageVideo($event)"
                          @change="checkUploadedFiles"
                          accept=".jpg,.png,.jpeg"
                          ref="productVideo"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="text-section"
                  :class="{
                    'simple-data-tooltip': isCategorieGroupNameFocus,
                  }"
                  :data-tooltip-text="isCategorieGroupNameFocus && categoriesName"
                >
                  <img
                    alt="compulsory"
                    v-if="!categoriesName"
                    class="compulsory-field-category-group"
                    src="@/assets/images/asterisk.svg?skipsvgo=true"
                  />
                  <input
                    class="title text-title-1"
                    id="title-name"
                    @mouseover="checkEditCategorieName()"
                    @mouseleave="hideeditcategorynameTip()"
                    @input="hideeditcategorynameTip()"
                    @keydown="hideeditcategorynameTip()"
                    autocomplete="off"
                    placeholder="Name your category group"
                    v-model.trim="categoriesName"
                  />
                </div>
                <div class="image-details">
                  <span class="bold text-title-2">Category Group Image: </span>
                  <span class="normal text-title-2 font-normal"
                    >jpg,png format (recom. 1MB, max 15 MB)</span
                  >
                </div>
              </div>
              <div class="right-section-category">
                <div
                  :class="
                    categoriesState === 'published' ||
                    categoriesState === 'publishing'
                      ? 'publish-btn text-title-2'
                      : 'publish-btn text-title-2'
                  "
                >
                  <span
                    :class="
                      formatedCategory.length >= 1
                        ? 'text'
                        : 'text inactive-publish'
                    "
                  >
                    {{ $t('COMMON.PUBLISH') }}
                  </span>
                  <div
                    class="publish-toggle-section"
                    :class="{
                      'simple-data-tooltip simple-data-tooltip-edge': formatedCategory.length === 0 && categoriesName,
                    }"
                    :data-tooltip-text="(formatedCategory.length === 0 && categoriesName) && $t('CATEGORY.TOOL_TIP_CATEGORY')"
                  >
                    <label
                      :class="
                        formatedCategory.length >= 1
                          ? 'switch'
                          : 'switch inactive-publish'
                      "
                    >
                      <input
                        type="checkbox"
                        :checked="categoriesStatus == 'active'"
                        @click.prevent="
                          !categoriesName
                            ? publishToggleBtnPopup()
                            : !categoriesName || formatedCategory.length === 0
                            ? ''
                            : publishToggleBtn()
                        "
                      />
                      <span
                        class="slider-round"
                        :class="{
                          isSliderActive:
                            formatedCategory.length == 0 || !categoriesName,
                        }"
                      ></span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
            <div
              v-if="finalAvailableLangs && finalAvailableLangs.length > 1"
              class="category-variant-section"
            >
              <div class="category-variants-main">
                <div class="category-variants">Categories Group Variants:</div>
                <div
                  class="add-variant-section"
                  :class="{
                    'simple-data-tooltip simple-data-tooltip-edge': recipeVariantLanguageList.length < 1,
                  }"
                  :data-tooltip-text="recipeVariantLanguageList.length < 1 && $t('COMMON.ADD_ONLY_ONE_VARIANT')"
                >
                  <div
                    :class="
                      recipeVariantLanguageList.length > 0
                        ? 'add-variant-main'
                        : 'disable-add-variant-main add-variant-main'
                    "
                    @click="
                      recipeVariantLanguageList.length > 0
                        ? openRecipeVariantPopUp()
                        : ''
                    "
                  >
                    <div class="add-variant-btn">
                      <img alt="" src="@/assets/images/category-add.png" />
                    </div>
                    <div class="add-variant-text">Add variant</div>
                  </div>
                </div>
              </div>
              <div
                class="add-category-variant"
                v-if="
                  recipeVariantList.length < 1 ||hasEditCategoryGroupMessageEnabled
                "
              >
                Add categories group variants to support multiple languages.
              </div>
              <div v-else class="category-variant-card-main">
                <template v-for="(categoryVariant, index) in recipeVariantList">
                  <variant-card-field
                    v-if="categoryVariant?.lang !== lang"
                    v-model="categoryVariant.name"
                    :prefix-label="displayLanguageCode(categoryVariant.lang)"
                    input-placeholder="Enter name"
                    @input-change="inputContentChanged(index)"
                    @delete-action="deleteCategoryVariant(categoryVariant, index)"
                  ></variant-card-field>
                </template>
              </div>
            </div>
          </div>
        </div>
        <div
          :class="
            !isPageLoading
              ? 'categories-group-section'
              : 'categories-group-section categories-group-section-data'
          "
          v-if="formatedCategory.length != 0"
        >
          <div class="content">
            <div class="loading" v-if="isPageLoading">
              <div class="content">
                <div class="input-loading">
                  <div class="loader-image"></div>
                </div>
                <div class="loading-text">
                  <p>{{ $t('LOADER.LOADING') }}</p>
                </div>
              </div>
            </div>
            <div class="categories-header-section" v-if="!isPageLoading">
              <div
                v-if="formatedCategory.length <= 1"
                class="categories-header"
              >
                {{
                  formatedCategory && formatedCategory.length
                    ? formatedCategory.length
                    : 0
                }}
                Category in Group
              </div>
              <div
                v-else-if="formatedCategory.length > 1"
                class="categories-header"
              >
                {{
                  formatedCategory && formatedCategory.length
                    ? formatedCategory.length
                    : 0
                }}
                Categories in Group
              </div>
              <div class="add-categories-btn" @click="addCategory">
                <div class="add-category-option text-h3">
                  <img alt="add" src="@/assets/images/add_icon.png" />
                  <p>{{ $t('CATEGORY.ADD_CATEGORY') }}</p>
                </div>
              </div>
            </div>
            <div
              class="table-header"
              v-if="formatedCategory.length && !isPageLoading"
            >
              <div class="container text-h3">
                <div class="first-column"></div>
                <div class="category-group-isin">
                  <span>{{ $t('CATEGORY.CATEGORY_ISIN') }} </span>
                </div>
                <div class="category-group-title">
                  <span>{{ $t('CATEGORY.CATEGORY_TITLE') }}</span>
                </div>
                <div class="category-group-count">
                  <span> {{ $t('COMMON.RECIPE_COUNT') }}</span>
                </div>
                <div class="status">
                  <span>{{ $t('COMMON.STATUS') }}</span>
                </div>
              </div>
            </div>
            <div
              class="categories-table-content"
              v-if="formatedCategory.length > 0 && !isPageLoading"
            >
              <table class="categories-table" id="categories-table">
                <caption></caption>
                <tbody>
                  <draggable
                    :list="formatedCategory"
                    class="all-content"
                    :scroll-sensitivity="200"
                    :force-fallback="true"
                    ghost-class="hidden-list"
                    @start="drag = true"
                    @end="drag = false"
                    handle=".draggable-icon"
                  >
                    <tr
                      class="body"
                      v-for="(categories, index) in formatedCategory"
                      :key="index"
                    >
                      <th scope="col"></th>
                          <td>
                            <div class="draggable-icon">
                              <div class="instruction-drag-icon">
                                <img
                                  alt=""
                                  class="promoted-handle"
                                  src="@/assets/images/drag-vertically.svg?skipsvgo=true"
                                />
                              </div>
                            </div>
                          </td>
                      <td class="category-image">
                        <div class="image-categories">
                          <img
                            alt="category"
                            class="image"
                            :src="categories?.image || defaultImage"
                          />
                        </div>
                      </td>
                      <td class="category-isin">
                        <div class="category-isin">
                          {{ categories.isin ? categories.isin : "" }}
                        </div>
                      </td>
                      <td class="category-name">
                        <div
                          :class="{
                            'simple-data-tooltip': !isDraggableTooltipDisplay && isCategoryListTooltipVisible,
                          }"
                          :data-tooltip-text="(!isDraggableTooltipDisplay && isCategoryListTooltipVisible) && categories?.name"
                        >
                          <div
                            @mouseover="checkCategoryName(index)"
                            @mouseleave="hideCategorynameTip(index)"
                            :id="`category-item${index}`"
                            class="category-name-recipe text-title-2"
                          >
                            {{
                              categories?.name ?? ""
                            }}
                          </div>
                        </div>
                      </td>
                      <td class="category-recipes">
                        <div class="categories-details">
                          <span v-if="categories.totalRecipes > 1"
                            >{{ categories.totalRecipes }}
                            {{ $t('COMMON.RECIPES') }}</span
                          >
                          <span v-if="categories.totalRecipes <= 1"
                            >{{ categories.totalRecipes }} Recipe</span
                          >
                        </div>
                      </td>
                      <td class="category-published">
                        <div class="categories-btn">
                          <div
                            class="published-state text-light-h4"
                            v-if="categories.state === $t('TAG.PUBLISHED') "
                          >
                            <span
                              ><img
                                alt=""
                                src="@/assets/images/published-icon.png"
                              />{{ $t('COMMON.PUBLISHED') }}</span
                            >
                          </div>
                          <div
                            class="unpublished-state-categories"
                            v-if="categories.state === $t('TAG.UNPUBLISHED')"
                          >
                            <span
                              ><img
                                alt=""
                                src="@/assets/images/unpublished-icon.png"
                              />{{ $t('COMMON.UNPUBLISHED') }}</span
                            >
                          </div>
                          <div
                            class="preview-state"
                            v-if="categories.state === 'preview'"
                          >
                            <span>{{ $t('BUTTONS.PREVIEW_BUTTON') }}</span>
                          </div>
                          <div
                            class="failed-state"
                            v-if="categories.state === 'failed'"
                          >
                            <span>Failed</span>
                          </div>
                          <div
                            class="publishing-state-cat"
                            v-if="categories.state === 'publishing'"
                          >
                            <span
                              ><img
                                alt=""
                                src="@/assets/images/updating-icon.png"
                              />{{ $t('UPDATING') }}</span
                            >
                          </div>
                          <div
                            class="timeout-state"
                            v-if="categories.state === 'timeout'"
                          >
                            <span>timeout</span>
                          </div>
                        </div>
                        <div class="category-actions-wrapper">
                        <button
                          type="button"
                          class="edit-btn btn-reset"
                          @click="editCategories(categories || '')"
                          :class="{
                            'disable-category-group-edit-button simple-data-tooltip': categories?.state === 'publishing'
                          }"
                          :data-tooltip-text="categories?.state === 'publishing' && $t('COMMON.CANNOT_EDIT_WHILE_UPDATING')"
                        >
                          <img
                            alt="edit"
                            src="@/assets/images/edit_icon_black.png"
                          />
                        </button>
                        <button
                          type="button"
                          class="menu btn-reset"
                          :class="{
                            'disable-category-group-delete-button simple-data-tooltip': categories?.state === 'publishing'
                          }"
                          :data-tooltip-text="categories?.state === 'publishing' && $t('COMMON.CANNOT_DELETE_WHILE_UPDATING')"
                        >
                          <img
                            alt="delete"
                            class="table-edit-btn"
                            @click="deleteRecipeListPopUp(index, categories)"
                            src="@/assets/images/delete-icon.png"
                          />
                        </button>
                        </div>
                      </td>
                    </tr>
                  </draggable>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div
          class="add-category-recipes-section"
          v-if="formatedCategory.length == 0 && !isPageLoading"
        >
          <div class="content">
            <div class="left-section">
              <div class="head-text text-h2 font-normal">Add Categories to Category Group</div>
              <div class="sub-text">
                Add a group of categories to your new category group.
              </div>
              <button type="button"
                class="btn-green"
                @click="addCategory()"
                @keydown="preventEnterAndSpaceKeyPress($event)"
              >
                {{ $t('BUTTONS.ADD_CATEGORY') }}
              </button>
            </div>
            <div class="right-section">
              <div class="image-content">
                <img
                  alt="Pan"
                  class="image"
                  src="@/assets/images/pan-image.png"
                />
              </div>
            </div>
          </div>
        </div>
        <catModel
          :isUpdateCategoryModal="isUpdateCategoryModal"
          :closeModal="closeModal"
          :getCategorySearch="getCategorySearch"
          :isSearchExitEnable="isSearchExitEnable"
          :isTableDataLoading="isAddTableDataLoading"
          :formatedCategoryPopUp="formatedCategoryPopUp"
          :isChecked="isChecked"
          :defaultImage="defaultImage"
          :styleForTooltip="styleForTooltip"
          :checkAddCategoryGroupName="checkAddCategoryGroupName"
          :isCatGrpNameVisible="isCatGrpNameVisible"
          :hideAddCategoryGroupNameTip="hideAddCategoryGroupNameTip"
          :fromPopUp="fromPopUp"
          :sizePopUp="sizePopUp"
          :updateCategoriesTotal="updateCategoriesTotal"
          :preventEnterAndSpaceKeyPress="preventEnterAndSpaceKeyPress"
          :loadUpdatePopUpAsync="loadUpdatePopUpAsync"
          :countCategoriesSelected="countCategoriesSelected"
          :addCategoriesToGroupBtn="addCategoriesToGroupBtn"
          :hasNoCategoryFound="hasNoCategoryFound"
          :resetQuery="resetQuery"
          :queryCategory="queryCategory"
          :saveButtonMessage="'Done'"
          :categoriesTitle="'Add Categories to Cat. group'"
          :categoriesSubtitle="'Select categories to add to your group.'"
        />
        <deleteModal
          v-if="isDeleteCategoryImage"
          :closeModal="closeModal"
          :productInfoTitle="'Delete Image?'"
          :productDescriptionOne="'Are you sure you want to Delete '"
          :productDescriptionTwo="'this image?'"
          :deleteItem="deleteCategoryGroupImage"
          :availableLanguage="0"
          :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
        />
        <deleteModal
          v-if="isDeleteCategoryModal"
          :closeModal="closeModal"
          :productInfoTitle="$t('DESCRIPTION_POPUP.REMOVE_CATEGORY')"
          :productDescriptionOne="
            $t('DESCRIPTION_POPUP.REMOVE_CATEGORY_POPUP')
          "
          :productDescriptionTwo="$t('DESCRIPTION_POPUP.CATEGORIES_GROUP')"
          :deleteItem="deleteCategoryBtn"
          :availableLanguage="0"
          :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
        />
        <unableToContentModal
          v-if="hasUnableToPublishArticle"
          :closeModal="closeModal"
          :text="$t('TEXT_POPUP.NOT_PUBLISHED')"
        />
        <unpublishModal
          v-if="isUnPublishModalVisible"
          :description="'Do you want to unpublish this category group?'"
          :noteMessage="'Unpublishing this category group will remove it and all associated categories from the widget. Proceed?'"
          :buttonName="$t('BUTTONS.CONFIRM_BUTTON')"
          :unpublishFunction="unPublishConfirm"
          :closeModal="closeModal"
        />
        <saveModal
          v-if="isPublishModalVisible"
          :closeModal="closeModal"
          :saveAndPublishFunction="publishConfirm"
          :availableLang="[]"
          :buttonName="$t('BUTTONS.CONFIRM_BUTTON')"
          :description="$t('DESCRIPTION_POPUP.PUBLISH_POPUP')"
          imageName="@/assets/images/unpublished.png"
        />
        <saveModal
          v-if="isSaveModalVisible && categoriesStatus != 'active'"
          :closeModal="closeModal"
          :saveAndPublishFunction="saveButtonClickAsync"
          :availableLang="[]"
          :buttonName="$t('BUTTONS.SAVE_BUTTON')"
          :description="$t('DESCRIPTION_POPUP.SAVE_UPDATES_POPUP')"
          imageName="@/assets/images/1014367-MQuADjfW4ulIQ-en-US-0.png"
        />
        <saveModal
          v-if="isSaveModalVisible && categoriesStatus == 'active'"
          :closeModal="closeModal"
          :saveAndPublishFunction="saveButtonClickAsync"
          :availableLang="[]"
          :buttonName="'Publish'"
          :description="$t('DESCRIPTION_POPUP.PUBLISH_UPDATES_POPUP')"
          imageName="@/assets/images/publish-variant-icon.png"
        />
        <cancelModal
          v-if="isConfirmModalVisible"
          :availableLang="[]"
          :isCampaignModifiedFromShoppableReview="false"
          :callConfirm="backToCategory"
          :closeModal="closeModal"
        />
        <addVariant
          v-if="isAddVariantCategoryNamePopup"
          :closeModal="closeModal"
          :typeName="'Category Group'"
          :addVariantSelectedLanguage="recipeVariantSelectedLanguage"
          :itemName="categoriesName"
          @addConfirmVariant="addRecipeVariant"
          @preventEnterAndSpaceKeyPress="preventEnterAndSpaceKeyPress"
          @backToRoute="backToSelectLanguageVariantPopUp"
        />
        <selectTheLanguageModal
          v-if="hasRecipeVariantLanguagePopup"
          :closeModal="closeModal"
          @preventEnterAndSpaceKeyPress="preventEnterAndSpaceKeyPress"
          @nextVariantPopUp="nextCategoryVariantNameModalPopUp"
          @setRecipeVariantLanguageMatches="setRecipeVariantLanguageMatches"
          @showRecipeVariantLanguageMatches="showRecipeVariantLanguageMatches"
          :recipeVariantLanguageList="recipeVariantLanguageList"
          :hasRecipeVariantLanguageResult="hasRecipeVariantLanguageResult"
        />
        <invalidImageVideoPopup
          v-show="isInvalidImageModalVisible"
          :closeModal="closeModal"
          :acceptedFile="' jpg,png'"
          :video="false"
          :image="true"
          :zip="false"
        />
        <deleteModal
          v-if="isRemoveCategoryVariantVisible"
          :closeModal="closeModal"
          :productInfoTitle="'Remove Categories group Variant?'"
          :productDescriptionOne="'Are you sure you want to remove this variant from the'"
          :productDescriptionTwo="$t('DESCRIPTION_POPUP.CATEGORIES_GROUP')"
          :deleteItem="removeCategoryVariant"
          :availableLanguage="0"
          :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
        />
        <savingModal
          v-show="isCategorySaving"
          :status="categoriesStatus == 'active' ? 'publishing' : 'saving'"
        />
        <sizeLimit
          v-if="isUploadingImagePopup"
          :continueImage="continueImage"
          :maxFileSize="$t('DESCRIPTION_POPUP.LARGER_FILE')"
          :optimalImageSize="$t('DESCRIPTION_POPUP.OPTIMAL_IMAGE')"
          :closeModal="closeModal"
          :isUploadingImagePopup="isUploadingImagePopup"
        />
        <sizeLimit
          v-if="isMaxImagePopupVisible"
          :imageSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE_SIZE')"
          :fileSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE') "
          :closeModal="closeModal"
          :isMaxImagePopupVisible="isMaxImagePopupVisible"
        />
      </content-wrapper>
  </client-only>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance, watchEffect } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuth0 } from '@auth0/auth0-vue';
import catModel from "@/components/cat-model";
import unpublishModal from "@/components/unpublish-modal";
import savingModal from "@/components/saving-modal";
import invalidImageVideoPopup from "@/components/invalid-image-video-popup";
import cancelModal from "@/components/cancel-modal";
import sizeLimit from "@/components/size-limit.vue";
import saveModal from "@/components/save-modal";
import CategoriesService from "@/services/CategoriesService";
import RecipeService from "@/services/RecipeService";
import deleteModal from "@/components/delete-modal";
import addVariant from "@/components/add-variant";
import selectTheLanguageModal from "@/components/select-the-language";
import updateProtocol from "@/mixins/updateProtocol";
import axios from "axios";
import unableToContentModal from "@/components/unable-to-content-modal";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import defaultImage from "@/assets/images/default_recipe_image.png";
import { useProjectLang } from "@/composables/useProjectLang";
import { useStore } from "vuex";
import { useNuxtApp } from '#app'
import { QUERY_PARAM_KEY } from "../сonstants/query-param-key.js";

defineComponent({
  mixins: [updateProtocol],
});

const { $eventBus } = useNuxtApp();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const project = reactive({});
const isDeleteCategoryImage = ref(false);
const isCategorieGroupNameFocus = ref(false);
const isMaxImagePopupVisible = ref(false);
const groupIsin = ref("");
const uploadImageConfirm = ref("");
const isUploadingImagePopup = ref(false);
const isInvalidImageModalVisible = ref(false);
const isPageLoading = ref(false);
const isCategorySaving = ref(false);
const isDeleteCategoryModal = ref(false);
const categoriesStatus = ref("hidden");
const categoriesState = ref("");
const countCategoriesSelected = ref(0);
const isUpdateCategoryModal = ref(false);
const categoriesName = ref("");
const hasUnableToPublishArticle = ref(false);
const categoryUpdateList = ref([]);
const categoriesList = ref([]);
const totalCategoriesList = ref([]);
const selectedCategory = ref([]);
const indexOfCategory = ref(0);
const updateCategoriesTotal = ref(0);
const fromPopUp = ref(0);
const sizePopUp = ref(9);
const categoryIsin = ref("");
const totalCategoryIsins = ref([]);
const selectedCategoryIsins = ref([]);
const isSaveModalVisible = ref(false);
const isPublishModalVisible = ref(false);
const isUnPublishModalVisible = ref(false);
const formatedCategory = ref([]);
const formatedCategoryPopUp = ref([]);
const isCampaignModified = ref(false);
const isConfirmModalVisible = ref(false);
const editCategoriesIsin = ref("");
const queryCategory = ref("");
const isSearchExitEnable = ref(false);
const isAddTableDataLoading = ref(false);
const hasRecipeVariantLanguagePopup = ref(false);
const hasRecipeVariantLanguageResult = ref(false);
const recipeVariantLanguage = ref("");
const recipeVariantSelectedLanguage = ref("");
const recipeVariantLanguageList = ref([]);
const isAddVariantCategoryNamePopup = ref(false);
const variantName = ref("");
const recipeVariantList = ref([]);
const categoryVariantDataIndex = ref("");
const categoryAssociations = ref({});
const isRemoveCategoryVariantVisible = ref(false);
const selectedDefaultLang = ref([]);
const deletedvariant = ref(false);
const recipeVariantLanguageIndex = ref(0);
const finalSelectedLanguage = ref([]);
const saveRemovedCategoryGroupVariants = ref([]);
const initiallyVariantSupported = ref([]);
const hasEditCategoryGroupMessageEnabled = ref(false);
const isRecipeVariantNameEmpty = ref(false);
const isCatGrpNameVisible = ref(false);
const finalAvailableLangs = ref([]);
const lang = ref("");
const imageResponseUrl = ref("");
const styleForTooltip = reactive({
  visibility: "hidden",
});
const file = ref("");
const filesName = ref("");
const styleForDiv = reactive({ display: "none" });
const hasNoCategoryFound = ref(false);
const cancelImage = reactive({});
const uploadImagePercentage = ref(0);
const loadedImageSize = ref(0);
const uploadImageSize = ref(0);
const image = ref("");
const isDraggableTooltipDisplay = ref(false);
const isAdminCheck = ref(false);
const drag = ref(false);

const { getRef } = useRefUtils();
const { preventEnterAndSpaceKeyPress } = useEventUtils();
const { delay } = useDelayTimer();
const { triggerLoading } = useCommonUtils();
const router = useRouter();
const route = useRoute();
const auth = useAuth0();
const { readyProject, getProject, isAdmin, getDefaultLang, getAvailableLangs } = useProjectLang();
const store = useStore();
const isCategoryListTooltipVisible = ref(false);

onMounted(async () => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      project.value = await getProject();
      if (!(project.value && project.value.id)) {
        router.push({ path: "/create-project" });
        return;
      }
      isAdminCheck.value = isAdmin;
      lang.value = await getDefaultLang();
      finalAvailableLangs.value = await getAvailableLangs();
      getGroupISINAsync();
      document.addEventListener("input", handleClickNameInput);
      document.addEventListener("click", handleClickOutsidePopup);
      document.addEventListener("keyup", handleESCClickOutside);
      selectedDefaultLang.value.push(lang.value);
      if (finalAvailableLangs.value) {
        finalAvailableLangs.value.forEach((langCode) => {
          let langData = {};
          if (langCode === "es-US") {
            langData = {
              language: langCode,
              language_name: "Spanish",
              languageFlag: "/images/flags/spain-flag.png",
            };
          } else if (langCode === "fr-FR") {
            langData = {
              language: langCode,
              language_name: "French",
              languageFlag: "/images/flags/france-flag.png",
            };
          }
          if (!selectedDefaultLang.value.includes(langCode)) {
            recipeVariantLanguageList.value.push(langData);
          }
        });
      }
    }
  });
});
onBeforeUnmount(() => {
  document.removeEventListener("input", handleClickNameInput);
  document.removeEventListener("click", handleClickOutsidePopup);
  document.removeEventListener("keyup", handleESCClickOutside);
});

const deleteCategoryGroupImage = () => {
  imageResponseUrl.value = "";
  image.value = "";
  isDeleteCategoryImage.value = false;
};

const DeleteImage = () => {
  isDeleteCategoryImage.value = true;
};
const backToCategory = () => {
  if (editCategoriesIsin.value) {
    editCategoriesConfirm(editCategoriesIsin.value);
  } else {
    backToCategoriesConfirm();
    closeModal();
  }
};
const handleESCClickOutside = (event) => {
  if (event?.key === "Escape") {
    closeModal();
  }
};
const checkAddCategoryGroupName = (index, isin) => {
  let name = getRef("addCategoryGroupName" + index + isin);
  if (name.scrollWidth > name.clientWidth) {
    isCatGrpNameVisible.value = true;
  }
};

const hideAddCategoryGroupNameTip = (index, isin) => {
  let name = getRef("addCategoryGroupName" + index + isin);
  if (name.scrollWidth > name.clientWidth) {
    isCatGrpNameVisible.value = false;
  }
};
const uploadSameImageVideo = (event) => {
  event.target.value = "";
};

const checkUploadedFiles = (event) => {
  isCampaignModified.value = true;
  const evnt = event;
  file.value = evnt.target.files || evnt.srcElement.files;
  const fileType = file.value[0].type.split("/")[0];
  if (fileType === "image") {
    uploadFiles();
  } else {
    isInvalidImageModalVisible.value = true;
  }
};
const uploadImageFile = (url, file) => {
  cancelImage.value = axios && axios.CancelToken ? axios.CancelToken.source() : {};
  axios
    .put(url, file, {
      headers: {
        "Content-Type": file.type,
        "x-amz-acl": "public-read",
      },
      cancelToken: cancelImage.value.token,
      onUploadProgress: function (progressEvent) {
        uploadImagePercentage.value = parseInt(
          Math.round((progressEvent.loaded / progressEvent.total) * 100)
        );
        uploadedImageFunctionAsync(uploadImagePercentage.value);
        loadedImageSize.value = progressEvent.loaded;
      },
    })
    .then(() => {})
    .catch((e) => {
      if (axios.isCancel(e)) {
        console.error("Image request canceled.");
      } else {
        console.error(e);
      }
    });
};
const uploadedImageFunctionAsync = async (data) => {
  if (data === 100) {
    uploadImagePercentage.value = 99;
    await delay(2000);
    uploadImagePercentage.value = 100;
  }
};
const inputContentChanged = (index) => {
  isCampaignModified.value = true;
};
const checkEditCategorieName = () => {
  let name = getRef("title-name");
  if (
    name.scrollWidth > name.clientWidth &&
    name !== document.activeElement &&
    categoriesName.value?.trim().length > 0
  ) {
    isCategorieGroupNameFocus.value = true;
  }
};
const hideeditcategorynameTip = () => {
  isCategorieGroupNameFocus.value = false;
};
const checkVariantNameEmpty = () => {
  if (!recipeVariantList.value?.length) {
    isRecipeVariantNameEmpty.value = false;
    return;
  }

  let count = 0;
  recipeVariantList.value.forEach((data) => {
    let variantName = data.name;
    if (variantName.trim().length === 0) {
      count++;
    }
  });

  isRecipeVariantNameEmpty.value = count !== 0;
};
const checkCategoryName = (index) => {
  let name = getRef("category-item" + index);
  if (name.scrollWidth > name.clientWidth) {
    isCategoryListTooltipVisible.value = true;
  }
};
const hideCategorynameTip = (index) => {
  let name = getRef("category-item" + index);
  if (name.scrollWidth > name.clientWidth) {
    isCategoryListTooltipVisible.value = false;
  }
};
const displayLanguageCode = (item) => {
  let arr = [];
  let selectedCode = "";
  if (item != lang.value) {
    arr = item.split("-");
    selectedCode = arr[0].toUpperCase();
  }
  return selectedCode;
};
const addRecipeVariant = (item) => {
  variantName.value = item;
  if (variantName.value != "") {
    let categories_isin = [];
    if (formatedCategory.value && formatedCategory.value.length > 0) {
      formatedCategory.value.forEach((data) => {
        categories_isin.push(data.isin);
      });
    }

    let newVariantData = {
      name: item.trim(),
      lang: recipeVariantLanguage.value,
      categories: categories_isin || [],
    };

    recipeVariantList.value.push(newVariantData);
    isAddVariantCategoryNamePopup.value = false;
    variantName.value = "";
    isCampaignModified.value = true;

    recipeVariantLanguageList.value.forEach((data, index) => {
      if (data.language == recipeVariantLanguage.value) {
        recipeVariantLanguageList.value.splice(index, 1);
      }
    });

    if (saveRemovedCategoryGroupVariants.value.length) {
      saveRemovedCategoryGroupVariants.value.forEach((data, index) => {
        if (data == recipeVariantLanguage.value) {
          saveRemovedCategoryGroupVariants.value.splice(index, 1);
        }
      });
    }
  }
};
const nextCategoryVariantNameModalPopUp = (item) => {
  if (item === "") {
    recipeVariantSelectedLanguage.value = recipeVariantLanguageList.value[0].language;
    recipeVariantLanguage.value = recipeVariantLanguageList.value[0].language;
  } else {
    recipeVariantSelectedLanguage.value = item;
  }
  hasRecipeVariantLanguagePopup.value = false;
  isAddVariantCategoryNamePopup.value = true;
  hasRecipeVariantLanguageResult.value = false;
};
const showRecipeVariantLanguageMatches = () => {
  hasRecipeVariantLanguageResult.value = !hasRecipeVariantLanguageResult.value;
};
const backToSelectLanguageVariantPopUp = () => {
  isAddVariantCategoryNamePopup.value = false;
  hasRecipeVariantLanguagePopup.value = true;
};
const openRecipeVariantPopUp = () => {
  hasRecipeVariantLanguagePopup.value = true;
  hasRecipeVariantLanguageResult.value = false;
  recipeVariantLanguage.value = "";
};
const setRecipeVariantLanguageMatches = (value, index) => {
  recipeVariantLanguage.value = value.language;
  recipeVariantLanguageIndex.value = index;
  hasRecipeVariantLanguageResult.value = false;
  recipeVariantLanguageList.value.forEach((data, idx) => {
    if (data.language === recipeVariantLanguage.value) {
      recipeVariantLanguageList.value.splice(idx, 1);
      recipeVariantLanguageList.value.unshift(data);
    }
  });
};
const getGroupISINAsync = async () => {
  const payload = {
    user: auth?.user?.email,
    entity: "recipeGroup"
  };
  try {
    await store.dispatch("isin/getNewISINsAsync", { payload, lang: lang.value });
    const response = store.getters['isin/getISIN'];
    groupIsin.value = response?.isin ?? "";
  } catch (e) {
    console.error(e);
  }
};
const deleteCategoryVariant = (data, index) => {
  isRemoveCategoryVariantVisible.value = true;
  categoryVariantDataIndex.value = index;
  deletedvariant.value = data.lang;
};
const removeCategoryVariant = () => {
  initiallyVariantSupported.value.forEach((data, index) => {
    if (data.lang === deletedvariant.value) {
      saveRemovedCategoryGroupVariants.value.push(deletedvariant.value);
      initiallyVariantSupported.value.splice(index, 1);
    }
  });
  recipeVariantList.value.splice(categoryVariantDataIndex.value, 1);

  const langData = {
    language: deletedvariant.value,
    language_name: deletedvariant.value === "es-US" ? "Spanish" : "French",
    languageFlag: deletedvariant.value === "es-US" ? "/images/flags/spain-flag.png" : "/images/flags/france-flag.png"
  };

  recipeVariantLanguageList.value.push(langData);
  isCampaignModified.value = true;
  closeModal();
};
const deleteRecipeListPopUp = (index, categories) => {
  if (categories.state === "publishing") return;

  categoryIsin.value = categories.isin;
  indexOfCategory.value = index;
  isDeleteCategoryModal.value = true;
};
const deleteCategoryBtn = () => {
  isCampaignModified.value = true;
  formatedCategory.value.splice(indexOfCategory.value, 1);
  if (!formatedCategory.value.length) unPublishConfirm();

  closeModal();
  categoryIsin.value = "";
  indexOfCategory.value = 0;
  triggerLoading($keys.KEY_NAMES.DELETED);
};
const saveButtonClickAsync = async () => {
  isCategorySaving.value = true;
  await postUpdateCategoriesGroupAsync();
  isCampaignModified.value = false;
};
const publishToggleBtn = () => {
  if (categoriesStatus.value === "active") isUnPublishModalVisible.value = true;
  if (categoriesStatus.value === "hidden") isPublishModalVisible.value = true;
};
const patchPublishCategoryAsync = async () => {
  if (groupIsin.value && categoriesStatus.value) {
    const payload = { status: categoriesStatus.value }
    try {
      await store.dispatch("categoriesGroup/patchCategoryAsync", {
        isin: groupIsin.value,
        payload,
      })
      await delay(3000)
      isSaveModalVisible.value = false
      isCategorySaving.value = false
      router.push("/cat-group")
      const loadingStatus = categoriesStatus.value !== "active" ? "savedSuccess" : "isPublishedData"
      triggerLoading(loadingStatus)
    } catch (error) {
      showLoader.value = false
      isCategorySaving.value = false
    }
  }
}

const getCategorySearch = (queryCategoryValue) => {
  if (queryCategoryValue !== "") {
    isAddTableDataLoading.value = true
    queryCategory.value = queryCategoryValue
    updateCategoriesTotal.value = 0
    fromPopUp.value = 0
    formatedCategoryPopUp.value = []
    categoriesList.value = []
    getCategoryListAsync()
  }
}
const resetQuery = () => {
  isAddTableDataLoading.value = true
  updateCategoriesTotal.value = 0
  fromPopUp.value = 0
  formatedCategoryPopUp.value = []
  categoriesList.value = []
  queryCategory.value = ""
  getCategoryListAsync()
  isSearchExitEnable.value = false
  hasNoCategoryFound.value = false
}
const isChecked = (item) => {
  countCategoriesSelected.value = 0
  if (item.isChecked) {
    item.isChecked = false
    selectedCategoryIsins.value = selectedCategoryIsins.value.filter(data => data !== item.isin)
    selectedCategory.value = selectedCategory.value.filter(data => data.isin !== item.isin)
  } else {
    item.isChecked = true
    const selectedItem = categoryUpdateList.value.find(data => data.isin === item.isin)
    if (selectedItem) {
      selectedCategoryIsins.value.push(selectedItem.isin)
      selectedCategory.value.push(selectedItem)
    }
  }
  countCategoriesSelected.value = selectedCategoryIsins.value.length
}
const publishToggleBtnPopup = () => {
  if (categoriesStatus.value !== "active") {
    hasUnableToPublishArticle.value = true
  } else {
    publishToggleBtn()
  }
}
const addCategoryButton = () => {
  fromPopUp.value = 0
  isAddTableDataLoading.value = true
  getCategoryListAsync()
}
const loadUpdatePopUpAsync = async () => {
  const from = parseInt(fromPopUp.value) + sizePopUp.value
  if (from < updateCategoriesTotal.value) {
    fromPopUp.value = from
    const loadMoreRecipeList = await getCategoryListAsync()
    if (loadMoreRecipeList && categoryUpdateList.value) {
      await getCategoryStatisticsPopUpAsync(loadMoreRecipeList)
      categoryUpdateList.value = [
        ...categoryUpdateList.value,
        ...loadMoreRecipeList,
      ]
    }
  }
}
const addCategoriesToGroupBtn = () => {
  totalCategoryIsins.value = [];
  totalCategoriesList.value = [];
  isCampaignModified.value = true;
  if (selectedCategory.value && formatedCategory.value) {
    totalCategoriesList.value = [
      ...selectedCategory.value,
      ...formatedCategory.value,
    ];
  }
  formatedCategory.value = totalCategoriesList.value;
  closeModal();
};
const getCategoryStatisticsPopUpAsync = async (loadMoreRecipeList = []) => {
  let isins = [];
  const listToMap = loadMoreRecipeList.length ? loadMoreRecipeList : categoryUpdateList.value;
  isins = listToMap.map((data) => data.isin);
  const payload = {
    type: "category",
    country: lang.value.split('-')[1],
    lang: lang.value.split('-')[0],
    isins: isins.join(',')
  };

  try {
    await store.dispatch("categoriesGroup/getCategoryStatisticsAsync", { payload });
    const response = store.getters['categoriesGroup/getCategoryStatistics'];
    hasNoCategoryFound.value = false;

    listToMap.forEach((data) => {
      const matchingItem = response.find(item => item.isin === data.isin);
      if (matchingItem) {
        data.totalRecipes = matchingItem.totalRecipes;
      }
    });
    if (!loadMoreRecipeList.length) {
      formatedCategoryPopUp.value = [...categoryUpdateList.value];
    } else {
      formatedCategoryPopUp.value = [...categoryUpdateList.value, ...loadMoreRecipeList];
    }
  } catch {
    showLoader.value = false;
    hasNoCategoryFound.value = true;
  }
};
const getCategoryListAsync = async () => {
  const payload = {
    lang: lang.value,
    q: queryCategory.value,
    from: fromPopUp.value,
    size: sizePopUp.value,
    sort: $keys.EVENT_KEY_NAMES.SORT_BY,
  };

  try {
    await store.dispatch("categories/getCategoryMasterData", { payload });
    const response = store.getters["categories/getCategoryMasterData"];
    response.results = response.results?.slice(0, 9) || [];
    response.results.forEach(data => {
      data.isChecked = false;
      data.isAlreadyAddedCategory = false;
    });

    selectedCategoryIsins.value.forEach(category => {
      response.results.forEach(data => {
        if (category === data.isin) {
          data.isChecked = true;
        }
      });
    });

    if (fromPopUp.value >= 9) {
      response.results.forEach(data => {
        formatedCategory.value.forEach(item => {
          if (data.isin === item.isin) {
            data.isAlreadyAddedCategory = true;
          }
        });
      });
      return response.results;
    } else {
      response.results.forEach(data => {
        formatedCategory.value.forEach(item => {
          if (data.isin === item.isin) {
            data.isAlreadyAddedCategory = true;
          }
        });
      });
      categoryUpdateList.value = response.results ?? [];
      if (categoryUpdateList.value.length) {
        await getCategoryStatisticsPopUpAsync();
      }
    }
    isSearchExitEnable.value = !!queryCategory.value;
    updateCategoriesTotal.value = response.total;
    isAddTableDataLoading.value = false;
  } catch {
    showLoader.value = false;
    isPageLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  }
};
const updatedRecipeVariantList = (variantList) => {
  finalSelectedLanguage.value = [];
  let categories_isin = [];
  if (formatedCategory.value && formatedCategory.value.length > 0) {
    formatedCategory.value.forEach((data) => {
      categories_isin.push(data.isin);
    });
  }
  if (variantList && variantList.length > 0) {
    variantList.forEach((item) => {
      if (item && item.name !== "" && item.lang !== "") {
        item[item.lang] = {
          name: item.name ? item.name : "",
          categories: categories_isin || [],
        };
      }
    });
  }
  let updatedVariantList = Object.assign({}, ...variantList);
  if (updatedVariantList && updatedVariantList.lang) {
    delete updatedVariantList.lang;
  }
  if (updatedVariantList && updatedVariantList.name) {
    delete updatedVariantList.name;
  }
  if (updatedVariantList && updatedVariantList.categories) {
    delete updatedVariantList.categories;
  }
  finalSelectedLanguage.value = Object.keys(updatedVariantList);
  return updatedVariantList;
};
const setLanguageVariant = (variantList) => {
  const copyObjectData = [];
  if (finalSelectedLanguage.value && finalSelectedLanguage.value.length > 0 && variantList && variantList[lang.value]) {
    finalSelectedLanguage.value.forEach((item) => {
      const object = {
        [item]: variantList[lang.value],
      };
      copyObjectData.push(object);
    });
  }
  return Object.assign({}, ...copyObjectData);
};
const setPayLoadWithVariantAsync = async (payload) => {
  if (payload.image && payload.image[lang.value]) {
    payload.image = await setLanguageVariant(payload.image);
  }
  return payload;
};
const postUpdateCategoriesGroupAsync = async () => {
  const categories_isin = [];
  if (formatedCategory.value && formatedCategory.value.length > 0) {
    formatedCategory.value.forEach((data) => {
      categories_isin.push(data.isin);
    });
  }

  const defaultVariantData = {
    name: categoriesName.value.trim(),
    lang: lang.value,
    categories: categories_isin || [],
  };

  recipeVariantList.value.push(defaultVariantData);

  if (groupIsin.value) {
    let payload = {
      isin: groupIsin.value,
      type: "categoryGroup",
      data: updatedRecipeVariantList(recipeVariantList.value),
      image: {
        [lang.value]: imageResponseUrl.value
          ? imageResponseUrl.value.replace(/\?.*/, "")
          : "",
      },
    };

    if (imageResponseUrl.value) {
      payload = await setPayLoadWithVariantAsync(payload);
    } else {
      delete payload.image;
    }

    return store.dispatch("categoriesGroup/postCategoryOrCategoryGroupAsync", {
      payload
    })
      .then(async () => {
        if (saveRemovedCategoryGroupVariants.value.length) {
          await deleteVariant();
        }
        await patchPublishCategoryAsync();
      })
      .catch(() => {
        showLoader.value = false;
        isCategorySaving.value = false;
      });
  }
};

const deleteVariant = () => {
  return CategoriesService.deleteLanguageVariant(
    project.value,
    store,
    auth,
    saveRemovedCategoryGroupVariants.value,
    groupIsin.value
  )
    .then(() => {})
    .catch((e) => {
      console.error(e);
    });
};
const uploadImageAsync = async () => {
  if (file.value) {
    const reader = new FileReader();
    reader.onload = async () => {
      const extension = file.value[0].type.split("/")[1];
      const params = {
        entity: "recipeCategory",
        content: "image",
        lang: lang.value,
        extension: extension,
        public: true,
      };
      if (groupIsin.value) {
        await store.dispatch("preSignedUrl/getPreSignedImageUrlAsync", {
          isin: groupIsin.value,
          params,
        });
        const response = store.getters['preSignedUrl/getPreSignedUrl'];
        const uploadedImage = response?.data?.url ?? "";
        await uploadImageFile(uploadedImage, file.value[0]);
        await RecipeService.upload(uploadedImage, file.value[0]);
        imageResponseUrl.value = uploadedImage;
      }
    };

    if (file.value[0]) {
      reader.readAsDataURL(file.value[0]);
    }
  }
};
const uploadSameImage = (event) => {
  event.target.value = "";
};
const continueImage = async () => {
  file.value = uploadImageConfirm.value;
  const reader = new FileReader();
  reader.addEventListener("load", async () => {
    image.value = reader.result;
    if (image.value) {
      loadedImageSize.value = 0;
      uploadImagePercentage.value = 1;
      await uploadImageAsync();
    }
  }, false);

  if (file.value[0]) {
    reader.readAsDataURL(file.value[0]);
  }
};

const uploadFiles = async () => {
  isCampaignModified.value = true;
  if (file.value.length > 0) {
    filesName.value = file.value[0].name.toLowerCase();
    const reg = /(.*?)\.(jpg|png|jpeg)$/;
    if (!filesName.value.match(reg)) {
      isInvalidImageModalVisible.value = true;
      file.value = "";
      filesName.value = "";
      return;
    }

    const fileSize = file.value[0].size;
    uploadImageSize.value = fileSize;
    const size = parseInt(fileSize.toFixed(0));

    if (size >= 1 * 1024 * 1024) {
      isUploadingImagePopup.value = true;
      uploadImageConfirm.value = file.value;
      file.value = "";
    }

    if (size >= 15 * 1024 * 1024) {
      file.value = "";
      isUploadingImagePopup.value = false;
      isMaxImagePopupVisible.value = true;
      return;
    } else {
      const reader = new FileReader();
      reader.addEventListener("load", async () => {
        image.value = reader.result;
        if (image.value) {
          loadedImageSize.value = 0;
          uploadImagePercentage.value = 1;
          await uploadImageAsync();
        }
      }, false);

      if (file.value[0]) {
        reader.readAsDataURL(file.value[0]);
      }
    }
  }
};
const addCategory = () => {
  formatedCategoryPopUp.value = [];
  categoryUpdateList.value = [];
  addCategoryButton();
  countCategoriesSelected.value = 0;
  selectedCategoryIsins.value = [];
  isUpdateCategoryModal.value = true;
};
const closeModal = () => {
  const scroll = getRef("updateGroupContent");
  scroll.scrollTo(0, 0);
  isConfirmModalVisible.value = false;
  isDeleteCategoryModal.value = false;
  isUpdateCategoryModal.value = false;
  isDeleteCategoryImage.value = false;
  isPublishModalVisible.value = false;
  isUnPublishModalVisible.value = false;
  isSaveModalVisible.value = false;
  isAddVariantCategoryNamePopup.value = false;
  isRemoveCategoryVariantVisible.value = false;
  hasUnableToPublishArticle.value = false;
  hasRecipeVariantLanguagePopup.value = false;
  isSearchExitEnable.value = false;
  isUploadingImagePopup.value = false;
  updateCategoriesTotal.value = 0;
  fromPopUp.value = 0;
  formatedCategoryPopUp.value = [];
  categoriesList.value = [];
  queryCategory.value = "";
  selectedCategory.value = [];
  selectedCategoryIsins.value = [];
  isInvalidImageModalVisible.value = false;
  isMaxImagePopupVisible.value = false;
  variantName.value = "";
};
const editCategories = (categories) => {
  if (categories.state === "publishing") {
    return;
  }
  editCategoriesIsin.value = categories.isin;
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    editCategoriesConfirm(categories.isin);
  }
};
const editCategoriesConfirm = (isin) => {
  router.push({
    path: `/category/${isin}`,
    query: {
      [QUERY_PARAM_KEY.FROM]: groupIsin.value,
      [QUERY_PARAM_KEY.BACK_FROM]: route.name,
    },
  });
};
const backToCategories = () => {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    backToCategoriesConfirm();
  }
};
const backToCategoriesConfirm = () => {
  isCampaignModified.value = false;
  router.push({
    path: '/cat-group',
    query: {
      [QUERY_PARAM_KEY.PAGE]: route.query[QUERY_PARAM_KEY.BACK_FROM],
    },
  });
};
const displayPopup = () => {
  isSaveModalVisible.value = true;
};
const unPublishConfirm = () => {
  isCampaignModified.value = true;
  categoriesStatus.value = 'hidden';
  closeModal();
};

const publishConfirm = () => {
  isCampaignModified.value = true;
  categoriesStatus.value = 'active';
  closeModal();
};

const handleClickNameInput = (event) => {
  if(getRef("title-name").contains(event.target)) {
      isCampaignModified.value = true;
    }
};
const handleClickOutsidePopup = (event) => {
  if (hasRecipeVariantLanguageResult.value) {
    const dropdown = document.querySelector('.category-group-dropdown');
    if (!dropdown.contains(event.target)) {
      hasRecipeVariantLanguageResult.value = false;
    }
  }
};
watchEffect(() => {
  if (drag.value) {
    isCampaignModified.value = drag.value;
    isDraggableTooltipDisplay.value = true;
    $eventBus.emit('campaignModified', isCampaignModified.value);
  } else {
    isDraggableTooltipDisplay.value = false;
    $eventBus.emit('campaignModified', isCampaignModified.value);
  }

  checkVariantNameEmpty();
});

</script>
