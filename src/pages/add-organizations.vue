<template>
    <content-wrapper class="padding-zero">
      <div class="background-image-add-organizations">
        <img alt="" class="background-image" :src="`${image}`" />
        <div @click="confirmBack()" class="back-btn">
          <img
            alt=""
            class="back-arrow-image"
            src="~/assets/images/back-arrow.png"
          />
          <span class="back-to-organizations text-title-2">{{
            $t("ORGANIZATION.BACK_TO_ORGANIZATIONS")
          }}</span>
        </div>
        <div class="head-btn">
          <button
            type="button"
            @click="saveOrganizations()"
            @keydown="preventEnterAndSpaceKeyPress($event)"
            :class="
              organizationsName.trim() !== '' &&
              isCampaignModified &&
              !isImageUploading &&
              (uploadImagePercentage === 0 || uploadImagePercentage === 100)
                ? 'btn-green'
                : 'disabled-button btn-green'
            "
          >
            {{ $t("BUTTONS.PUBLISH_BUTTON") }}
          </button>
          <button
            type="button"
            @click="confirmBack()"
            @keydown="preventEnterAndSpaceKeyPress($event)"
            class="btn-green-outline"
          >
            {{ $t("BUTTONS.CANCEL_BUTTON") }}
          </button>
        </div>
      </div>
      <div class="input-organizatons-section">
        <div class="organizatons-input-section">
          <div class="organizatons-left-section">
            <div
              :style="{ backgroundImage: image ? `url('${image}')` : '' }"
              class="image-section"
              id="categoryImage"
            >
              <div
                @click="deleteImagePopup()"
                v-show="
                  image &&
                  (uploadImagePercentage == 0 || uploadImagePercentage == 100)
                "
                class="organizations-delete-icon"
              >
                <img
                  alt=""
                  class="delete-organizations-icon-image"
                  src="@/assets/images/deleteVideoBtn.png"
                />
              </div>
              <div class="image-main-div" id="recipeVideo">
                <div class="image-inner-container">
                  <div
                    class="progress-image"
                    v-show="
                      uploadImagePercentage >= 1 && uploadImagePercentage <= 99
                    "
                  >
                    <div class="progress-image-content">
                      <div
                        v-show="
                          uploadImagePercentage >= 1 && uploadImagePercentage <= 5
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-0.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 6 &&
                          uploadImagePercentage <= 11
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-6.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 12 &&
                          uploadImagePercentage <= 17
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-12.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 18 &&
                          uploadImagePercentage <= 24
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-18.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 25 &&
                          uploadImagePercentage <= 30
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-25.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 31 &&
                          uploadImagePercentage <= 36
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-31.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 37 &&
                          uploadImagePercentage <= 41
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-37.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 42 &&
                          uploadImagePercentage <= 49
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-42.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 50 &&
                          uploadImagePercentage <= 55
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-50.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 56 &&
                          uploadImagePercentage <= 61
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-56.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 62 &&
                          uploadImagePercentage <= 67
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-62.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 68 &&
                          uploadImagePercentage <= 74
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-68.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 75 &&
                          uploadImagePercentage <= 80
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-75.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 81 &&
                          uploadImagePercentage <= 86
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-81.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 87 &&
                          uploadImagePercentage <= 92
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-87.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 93 &&
                          uploadImagePercentage <= 98
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-93.svg?skipsvgo=true"
                        />
                      </div>
                      <div v-show="uploadImagePercentage == 99">
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-uploaded.svg?skipsvgo=true"
                        />
                      </div>
                      <div class="upload-text">
                        <div
                          class="upload-heading text-light-h4"
                          v-if="
                            uploadImagePercentage >= 1 &&
                            uploadImagePercentage <= 98
                          "
                        >
                          Upload is in progress
                        </div>
                        <div class="upload-heading text-light-h4" v-else>
                          Uploaded
                        </div>
                        <span class="upload-media text-light-h6"
                          >{{ (loadedImageSize / 1024000).toFixed(1) }} of
                          {{ (uploadImageSize / 1024000).toFixed(1) }}
                          MB</span
                        >
                      </div>
                    </div>
                  </div>
                </div>
                <img
                  alt=""
                  v-if="
                    image &&
                    (uploadImagePercentage == 0 || uploadImagePercentage == 100)
                  "
                  class="display-image-section"
                  :src="`${image}`"
                />
                <div
                  class="replace-image-tag text-light-h4"
                  v-if="
                    uploadImagePercentage == 0 || uploadImagePercentage == 100
                  "
                >
                  <div class="hover-image">
                    <input
                      type="file"
                      class="upload-input"
                      title="Update Picture"
                      @click="uploadSameImageVideo($event)"
                      @change="checkUploadedFiles"
                      accept=".jpg,.png,.jpeg"
                      ref="productVideo"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div class="organizations-detail">
              <div
                class="organizations-text-section"
                :class="{
                  'simple-data-tooltip': isOrganizationNameFocused,
                }"
                :data-tooltip-text="isOrganizationNameFocused && organizationsName"
              >
                <input
                  ref="titleName"
                  type="text"
                  class="organizatons-title text-title-1"
                  autocomplete="off"
                  @input="campaignModified(), hideAddOrganizationNameTip()"
                  @mouseover="checkAddOrganizationName()"
                  @mouseleave="hideAddOrganizationNameTip()"
                  @keydown="hideAddOrganizationNameTip()"
                  placeholder="Name your organization"
                  v-model="organizationsName"
                  maxlength="50"
                />
                <span v-if="organizationsName == ''" class="compulsory-field">
                </span>
              </div>
              <div class="organizatons-image-details">
                <div class="organizatons-bottom-section">
                  <span class="font-bold">Image (optional): </span>
                  <span class="font-normal">jpg,png format (max 1 MB)</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <deleteModal
        v-if="isDeleteImageModal"
        :closeModal="closeModal"
        :productInfoTitle="$t('DELETE_IMAGE')"
        :productDescriptionOne="$t('DESCRIPTION_POPUP.DELETE_POPUP')"
        :productDescriptionTwo="'image?'"
        :deleteItem="deleteImageData"
        :availableLanguage="0"
        :buttonText="$t('BUTTONS.DELETE_BUTTON')"
      />
      <cancelModal
        v-if="isConfirmModalVisible"
        :availableLang="[]"
        :isCampaignModifiedFromShoppableReview="false"
        :callConfirm="backToOrganization"
        :closeModal="closeModal"
      />
      <saveModal
        v-if="isSaveModalVisible"
        :closeModal="closeModal"
        :saveAndPublishFunction="saveButtonClickAsync"
        :availableLang="[]"
        :buttonName="$t('BUTTONS.SAVE_BUTTON')"
        :description="$t('DESCRIPTION_POPUP.SAVE_UPDATES_POPUP')"
        :imageName="saveImage"
      />
      <savingModal v-show="isSavingOrganization" :status="'saving'" />
      <sizeLimit
        v-if="isMaxImagePopupVisible"
        :imageSizeAlert="'Your uploaded image size is larger than 1 MB.'"
        :fileSizeAlert="'Max. size for image: 1 MB'"
        :closeModal="closeModal"
        :isMaxImagePopupVisible="isMaxImagePopupVisible"
      />
      <invalidImageVideoPopup
        v-show="isInvalidImageModalVisible && !isOnline"
        :closeModal="closeModal"
        :acceptedFile="'jpg,png'"
        :video="false"
        :image="true"
        :zip="false"
      />
    </content-wrapper>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, watchEffect } from "vue";
import { useDelayTimer } from "~/composables/useDelayTimer";
import { useEventUtils } from "~/composables/useEventUtils";
import { useCommonUtils } from "~/composables/useCommonUtils";
import savingModal from "@/components/saving-modal";
import { useRouter, useRoute } from 'vue-router';
import sizeLimit from "@/components/size-limit.vue";
import invalidImageVideoPopup from "@/components/invalid-image-video-popup";
import cancelModal from "@/components/cancel-modal.vue";
import saveModal from "@/components/save-modal.vue";
import deleteModal from "@/components/delete-modal.vue";
import OrganizationsService from "@/services/OrganizationsService";
import axios from "axios";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import saveImage from "@/assets/images/1014367-MQuADjfW4ulIQ-en-US-0.png";
import { useNuxtApp } from '#app';
import { useStore } from "vuex";
import { useNetworkStatus } from '~/composables/useNetworkStatus';
import { useProjectLang } from "@/composables/useProjectLang";
import { QUERY_PARAM_KEY } from "../сonstants/query-param-key.js";


const project = ref({});
const isMaxImagePopupVisible = ref(false);
const isInvalidImageModalVisible = ref(false);
const isImageAvailable = ref(false);
const isConfirmModalVisible = ref(false);
const isSaveModalVisible = ref(false);
const isDeleteImageModal = ref(false);
const isCampaignModified = ref(false);
const isImageUploading = ref(false);
const isDataSaveInProgress = ref(false);
const newIsin = ref("");
const image = ref("");
const file = ref([]);
const organizationsName = ref("");
const isSavingOrganization = ref(false);
const cancelImage = ref({});
const imageResponseUrl = ref("");
const uploadImagePercentage = ref(0);
const loadedImageSize = ref(0);
const uploadImageSize = ref(0);
const imageResponseName = ref("");
const isOrganizationNameFocused = ref(false);
const titleName = ref(null);
const productVideo = ref(null);


const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { $eventBus, $auth } = useNuxtApp();
const { preventEnterAndSpaceKeyPress, onEscapeKeyPress } = useEventUtils();
const { triggerLoading, routeToPage } = useCommonUtils();
const { delay } = useDelayTimer();
const router = useRouter();
const route = useRoute();
const store = useStore();

const lang = computed(() => store.getters["userData/getDefaultLang"]);
const { isOnline } = useNetworkStatus();
const { readyProject, getProject } = useProjectLang();

onMounted(async () => {
  project.value = await getProject;
});

watchEffect(() => {
  $eventBus.emit('campaignModified', isCampaignModified.value);
  if (!isOnline.value) {
    closeModal();
  }
});
function checkAddOrganizationName() {
  if (
    titleName.value &&
    titleName.value.scrollWidth > titleName.value.clientWidth &&
    organizationsName.value.trim().length
  ) {
    isOrganizationNameFocused.value = true;
  }
}

function hideAddOrganizationNameTip() {
  isOrganizationNameFocused.value = false;
}
function uploadSameImageVideo(event) {
  event.target.value = "";
}
function checkUploadedFiles(event) {
  isCampaignModified.value = true;
  const evnt = event;
  file.value = evnt.target.files || evnt.srcElement.files;
  const fileType = file.value[0].type.split("/")[0];
  if (fileType === "image") {
    uploadFiles();
  } else {
    isInvalidImageModalVisible.value = true;
  }
}

const uploadImageFile = (url, file) => {
  cancelImage.value = axios.CancelToken.source();
  axios
    .put(url, file, {
      headers: {
        "Content-Type": file.type,
        "x-amz-acl": "public-read",
      },
      cancelToken: cancelImage.value.token,
      onUploadProgress: (progressEvent) => {
        uploadImagePercentage.value = Math.round(
          (progressEvent.loaded / progressEvent.total) * 100
        );
        uploadedImageFunctionAsync(uploadImagePercentage.value);
        loadedImageSize.value = progressEvent.loaded;
      },
    })
    .then(() => {
      // Handle success if needed
    })
    .catch((e) => {
      if (axios.isCancel(e)) {
        console.error("Image request canceled.");
      } else {
        console.error(e);
      }
    });
};
async function uploadedImageFunctionAsync(data) {
  if (data === 100) {
    uploadImagePercentage.value = 99;
    await delay(2000); // Make the transition to 100% more noticeable
    uploadImagePercentage.value = 100;
  }
}
function uploadSameImage(event) {
  event.target.value = "";
}
function deleteImageData() {
  isImageAvailable.value = false;
  isDeleteImageModal.value = false;
  image.value = "";
  imageResponseUrl.value = "";
  imageResponseName.value = "";
  file.value = [];
  image.value = ""; // Make sure to define image.value
}
async function getOrganizationsIsinsAsync() {
  const payload = {
    user: $auth?.user?.email,
    entity: "organization",
  };
  try {
    await store.dispatch("organizations/getNewISINsAsync", {
      payload,
      lang: lang.value,
    });
    const response = store.getters["organizations/getISIN"];
    newIsin.value = response?.isin ?? "";
  } catch (e) {
    isDataSaveInProgress.value = false;
    isSavingOrganization.value = false;
    console.error(e);
  }
}
function closeModal() {
  isConfirmModalVisible.value = false;
  isSaveModalVisible.value = false;
  isDeleteImageModal.value = false;
  isInvalidImageModalVisible.value = false;
  isMaxImagePopupVisible.value = false;
}
function backToOrganization() {
  isCampaignModified.value = false;
  $eventBus.emit("campaignModified", isCampaignModified.value);
  router.push({
    path: "/organizations",
    query: {
      [QUERY_PARAM_KEY.PAGE]: route.query[QUERY_PARAM_KEY.BACK_FROM],
    },
  });
}
function confirmBack() {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    backToOrganization();
  }
}
function saveOrganizations() {
  isSaveModalVisible.value = true;
}
function deleteImagePopup() {
  isDeleteImageModal.value = true;
}
function campaignModified() {
  isCampaignModified.value = true;
}
async function saveButtonClickAsync() {
  isDataSaveInProgress.value = true;
  isSavingOrganization.value = true;
  if (!newIsin.value) {
    await getOrganizationsIsinsAsync();
    await postOrganizationsAsync();
  } else {
    await postOrganizationsAsync();
  }
}
const uploadImageAsync = async () => {
  if (file.value && image.value) {
    const reader = new FileReader();
    reader.addEventListener(
      "load",
      async () => {
        const extension = file.value[0].type.split("/")[1];
        const payload = {
          entity: "organization",
          content: "image",
          extension: extension,
          lang: lang.value,
        };
        await store.dispatch("organizations/getPreSignedImageUrlAsync", {
          payload,
          isin: newIsin.value,
        });
        const response = store.getters["organizations/getPreSignedUrl"];
        await uploadImageFile(response.url, file.value[0]);
        await OrganizationsService.upload(response.url, file.value[0]);
        imageResponseUrl.value = response.url;
        imageResponseName.value = response.fileName;
      },
      false
    );
    if (file.value[0]) {
      reader.readAsDataURL(file.value[0]);
    }
  }
};
function uploadFiles() {
  isCampaignModified.value = true;
  if (file.value.length > 0) {
    const filesName = file.value[0].name.toLowerCase();
    const reg = /(.*?)\.(jpg|png|jpeg)$/;
    if (!filesName.match(reg)) {
      isInvalidImageModalVisible.value = true;
      file.value = [];
      return;
    }
    const fileSize = file.value[0].size;
    if (fileSize >= 1 * 1024 * 1024) {
      let videoElement = productVideo.value;;
      if (videoElement) {
        videoElement.blur();
      }
      file.value = [];
      isMaxImagePopupVisible.value = true;
      return;
    } else {
      const reader = new FileReader();
      reader.onload = async () => {
        image.value = reader.result;
        if (image.value) {
          loadedImageSize.value = 0;
          uploadImagePercentage.value = 1;
          if (!newIsin.value) {
            await getOrganizationsIsinsAsync();
          }
          await uploadImageAsync();
        }
      };
      if (file.value[0]) {
        reader.readAsDataURL(file.value[0]);
      }
    }
  }
}
async function postOrganizationsAsync() {
  const payload = {
    isin: newIsin.value,
    name: organizationsName.value.trim(),
    image: imageResponseName.value
      ? imageResponseName.value
      : image.value,
  };
  if (!payload?.image) {
    delete payload.image;
  }
  const params = {
    lang: lang.value,
    user: $auth.user.email,
  };
  try {
    await store.dispatch("organizations/postOrganizationsDataAsync", {
      payload,
      params,
    });
    isCampaignModified.value = false;
    isDataSaveInProgress.value = false;
    triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, isCampaignModified.value);
    isSavingOrganization.value = false;
    closeModal();
    routeToPage("organizations")
    triggerLoading("savedSuccess");
  } catch (error) {
    console.error(error);
    isDataSaveInProgress.value = false;
    isSavingOrganization.value = false;
  }
}
onEscapeKeyPress(closeModal);
</script>
